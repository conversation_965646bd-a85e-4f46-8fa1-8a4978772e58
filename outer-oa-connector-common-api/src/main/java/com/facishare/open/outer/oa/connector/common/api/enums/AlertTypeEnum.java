package com.facishare.open.outer.oa.connector.common.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息提醒类型枚举
 */
@Getter
@AllArgsConstructor
public enum AlertTypeEnum {

    /**
     * CRM待办提醒
     */
    CRM_TODO("CRM_TODO", "CRM待办"),

    

    /**
     * CRM日程提醒
     */
    CRM_SCHEDULE("CRM_SCHEDULE", "CRM日程"),

    /**
     * CRM审批提醒
     */
    CRM_NOTIFICATION("CRM_NOTIFICATION", "CRM提醒");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static AlertTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AlertTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}