package com.facishare.open.outer.oa.connector.i18n.qywx;

import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import com.google.common.base.Splitter;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 不需要spring管理的util
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@UtilityClass
public class I18nUtils {
    public String buildKey(String... suffixKeys) {
        StringBuilder i18nKey = new StringBuilder("erpdss_outer_oa_connector");
        for (String suffixKey : suffixKeys) {
            i18nKey.append(".").append(suffixKey);
        }
        return i18nKey.toString();
    }

    /**
     * 仅前端请求可用
     */
    public String getOrDefault(String i18nKey, String defaultValue) {
        String lang = getLocaleFromTrace();
        return I18nClient.getInstance().getOrDefault(i18nKey, 0, lang, defaultValue);
    }

    /**
     * 仅前端请求可用
     */
    public String getOrDefault(I18NStringEnum i18NStringEnum) {
        return getOrDefault(i18NStringEnum.getI18nKey(), i18NStringEnum.getI18nValue());
    }

    public static String getLocaleFromTrace() {
        String locale = TraceContext.get().getLocale();
        if (locale == null || locale.isEmpty()) {
            locale = "zh-CN";
        }
        return locale;
    }

    public static String getLang(String userAgent) {
        log.info("I18nUtils.getLang,userAgent={}",userAgent);
        List<String> list = Splitter.on(" ").splitToList(userAgent);
        for(String item : list) {
            if(StringUtils.containsIgnoreCase(item,"language")) {
                List<String> items = Splitter.on("/").splitToList(item);
                String lang = items.get(1);
                log.info("I18nUtils.getLang,lang={}",lang);
                if(StringUtils.containsIgnoreCase(lang,"zh")) {
                    lang = I18NStringManager.DEFAULT_LANG;
                }
                log.info("I18nUtils.getLang,lang2={}",lang);
                return lang;
            }
        }
        return I18NStringManager.DEFAULT_LANG;
    }
}
