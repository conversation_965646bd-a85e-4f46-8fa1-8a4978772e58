package com.facishare.open.aliyun.market.service;

import com.facishare.open.aliyun.market.BaseTest;
import com.facishare.open.aliyun.market.entity.OrderInfoEntity;
import com.facishare.open.aliyun.market.manager.OrderInfoManager;
import com.facishare.open.aliyun.market.model.OrderModel;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OrderEventServiceTest extends BaseTest {
    @Autowired
    private OrderEventService orderEventService;
    @Autowired
    private OrderInfoManager orderInfoManager;

    @Test
    public void test1() {
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId("218133857470914");
        orderModel.setOrderBizId("test00001");
        orderEventService.upgradeInstance(orderModel);
    }

    @Test
    public void test2() {
        OrderInfoEntity entity = orderInfoManager.getEntity("218039280500914");
        System.out.println(entity);
    }
}
