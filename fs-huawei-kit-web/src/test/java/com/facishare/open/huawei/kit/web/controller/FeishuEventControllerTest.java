package com.facishare.open.huawei.kit.web.controller;

import com.facishare.open.huawei.kit.web.BaseTest;

import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.controller.outer.HuaweiEventController;
import org.junit.Test;

import javax.annotation.Resource;

public class FeishuEventControllerTest extends BaseTest {
    @Resource
    private HuaweiEventController feishuEventController;

    @Test
    public void push() {
        Result push = feishuEventController.allOrgSyncV2(null, null);
        System.out.println(push);
    }
}
