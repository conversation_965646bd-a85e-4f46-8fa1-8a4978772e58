package com.facishare.open.huawei.kit.web.service.impl;

import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.huawei.kit.web.service.CorpService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("corpService")
public class CorpServiceImpl implements CorpService {
//    @Resource
//    private CorpInfoManager corpInfoManager;

//    @Override
//    public Result<CorpInfoEntity> getCorpEntity(String tenantKey) {
//        return Result.newSuccess(corpInfoManager.getEntityByTenantKey(tenantKey));
//    }
//
//    @Override
//    public Result<Void> insertCorpInfo(CorpInfoEntity corpInfoEntity) {
//        Integer count = corpInfoManager.updateCorpInfo(corpInfoEntity);
//        LogUtils.info("CorpServiceImpl.updateCorpInfo,count={}",count);
//        return Result.newSuccess();
//    }
}
