package manager;

import base.BaseAbstractTest;
import com.fxiaoke.common.Guard;
import com.fxiaoke.common.PasswordUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Test;

/**
 * <AUTHOR>
 * @Date 2020/10/27 10:18
 * @Version 1.0
 */
@Slf4j
public class mongoTest extends BaseAbstractTest {

        static String key = "FS4e2%Y#X@~g.+F<";
        static Guard encrypt = new Guard(key);
        public void testfunc() {
            try {
                //解密mongo的密文密码
                System.out.println(PasswordUtil.decode("2E967E1E9A6C4D7E4DB6D55BDFED2D5636F8762A548F674A34483B81E265B2AD"));
            }catch (Exception e) {

            }

        }
        public static void main( String args[] ) {
            mongoTest t = new mongoTest();
            t.testfunc();
        }


}
