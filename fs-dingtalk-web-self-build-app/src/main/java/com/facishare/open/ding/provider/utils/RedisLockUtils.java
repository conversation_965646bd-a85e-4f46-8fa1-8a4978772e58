package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.web.redis.RedisDataSource;
import com.github.jedis.support.MergeJedisCmd;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.Collections;

/**
 * <AUTHOR>
 * @Date 2021/2/28 12:48
 * @Version 1.0
 */
public class RedisLockUtils {

    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "EX";
    private static final Long RELEASE_SUCCESS = 1L;

    @Autowired
    private RedisDataSource redisDataSource;

    @PostConstruct
    public MergeJedisCmd mergeJedisCmd(){
        return redisDataSource.getRedisClient();
    }

    /**
     * 尝试获取分布式锁
     *
     * @param mergeJedisCmd Redis客户端
     * @param lockKey         锁
     * @param requestId       请求标识
     * @param expireTime      超期时间
     * @return 是否获取成功
     */
    public static boolean tryGetDistributedLock(MergeJedisCmd mergeJedisCmd, String lockKey, String requestId, int expireTime) {

        String result = mergeJedisCmd.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);

        if (LOCK_SUCCESS.equals(result)) {
            return true;
        }
        return false;

    }

    /**
     * 释放分布式锁
     *
     * @param mergeJedisCmd Redis客户端
     * @param lockKey         锁
     * @param requestId       请求标识
     * @return 是否释放成功
     */
    public static boolean releaseDistributedLock(MergeJedisCmd mergeJedisCmd, String lockKey, String requestId) {

        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Object result = mergeJedisCmd.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));

        if (RELEASE_SUCCESS.equals(result)) {
            return true;
        }
        return false;

    }

}
