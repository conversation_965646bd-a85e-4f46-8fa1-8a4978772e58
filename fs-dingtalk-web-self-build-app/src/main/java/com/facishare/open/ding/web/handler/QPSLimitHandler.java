package com.facishare.open.ding.web.handler;

import com.facishare.open.ding.web.config.ConfigCenter;
import com.facishare.open.ding.web.redis.RedisDataSource;
import com.facishare.open.ding.web.utils.RedisLockUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
@Slf4j
public class QPSLimitHandler {
    @Autowired
    private RedisDataSource redisDataSource;

    public static String QPS_LIMIT_FORMAT="DING_QPS_LIMIT_%S";
    public static String QPS_LIMIT_FORMAT_LOCK="DING_QPS_LIMIT_LOCK_%S";
    public static String DEFAULT="default";

    /**
     * 判断是否达到限流，限流后等待重试次数是否超过重试次数
     * @param ea 纷享企业ea
     * @return true:限流等待次数超过重试次数  false:未限流可以执行
     */
    public Boolean isQPSLimitByEa(String ea) {
        //先针对特殊需求的客户，加上限流
        if(!ConfigCenter.QPS_LIMIT_EA.contains(ea)) {
            //不在配置中，放行
            return Boolean.FALSE;
        }
        log.info("QPSLimitHandle.isQPSLimitByEa.ea={}", ea);
        String qpsLimitEa = String.format(QPS_LIMIT_FORMAT, ea);
        String qpsLimitLockEa = String.format(QPS_LIMIT_FORMAT_LOCK, ea);
        boolean flag = Boolean.TRUE;
        String uuid = UUID.randomUUID().toString();
        for (int i = 0; i < 5; i++) {
            boolean isGetLock = Boolean.FALSE;
            int sum = 1;
            while(true) {
                if(sum > 10) {
                    //累积超过10次未获取锁成功
                    break;
                }
                if(RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), qpsLimitLockEa, uuid, 10)) {
                    //获取锁成功
                    isGetLock = Boolean.TRUE;
                    break;
                }
                //未获取锁成功，累加失败次数
                sum ++;
            }
            //看看抢了多少次锁
            log.info("QPSLimitHandle.isQPSLimitByEa.ea={},sum={}", ea, sum);
            if(isGetLock == Boolean.TRUE) {
                Map<String, Integer> qpsLimitMap = new Gson().fromJson(ConfigCenter.QPS_LIMIT_MAX_EA, new TypeToken<Map<String, Integer>>(){}.getType());
                //这个是判断缓存是否存在
                boolean exists = redisDataSource.getRedisClient().exists(qpsLimitEa);
                //这个是判断缓存是否已经过期
                long pttl = redisDataSource.getRedisClient().pttl(qpsLimitEa);
                log.info("QPSLimitHandle.isQPSLimitByEa.ea={},exists={},pttl={}", ea, exists, pttl);
                if(!exists || pttl <= 0) {
                    //当前秒第一次调用钉钉接口
                    String result = redisDataSource.getRedisClient().set(qpsLimitEa, String.valueOf(qpsLimitMap.containsKey(ea) ? (qpsLimitMap.get(ea) - 1) : (qpsLimitMap.get(DEFAULT) - 1)));
                    long qpsMillis = 1000L - System.currentTimeMillis() % 1000L;
                    redisDataSource.getRedisClient().pexpire(qpsLimitEa, qpsMillis);
                    flag = Boolean.FALSE;
                    //释放分布式锁
                    boolean isRelease = RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), qpsLimitLockEa, uuid);
                    log.info("QPSLimitHandle.isQPSLimitByEa.ea={},result={},qpsMillis={},isRelease={}", ea, result, qpsMillis, isRelease);
                    break;
                }
                String limitCount = redisDataSource.getRedisClient().get(qpsLimitEa);
                log.info("QPSLimitHandle.isQPSLimitByEa.ea={},limitCount={}", ea, limitCount);
                if(StringUtils.isNotEmpty(limitCount) && Integer.parseInt(limitCount) > 0) {
                    //还有调用次数
                    String result = redisDataSource.getRedisClient().set(qpsLimitEa, String.valueOf(Integer.parseInt(limitCount) - 1));
                    //用DEL, SET, GETSET会将key对应存储的值替换成新的，命令也会清除掉超时时间
                    redisDataSource.getRedisClient().pexpire(qpsLimitEa, pttl);
                    flag = Boolean.FALSE;
                    //释放分布式锁
                    boolean isRelease = RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), qpsLimitLockEa, uuid);
                    log.info("QPSLimitHandle.isQPSLimitByEa.ea={},result={},isRelease={}", ea, result, isRelease);
                    break;
                } else {
                    //已无调用次数，释放锁，休眠，下次再尝试获取
                    boolean isRelease = RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), qpsLimitLockEa, uuid);
                    log.info("QPSLimitHandle.isQPSLimitByEa.ea={},isRelease={}", ea, isRelease);
                    try {
                        long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                        Thread.sleep(sleepMillis);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                log.info("QPSLimitHandle.isQPSLimitByEa.Failed to acquire lock,ea={}", ea);
                //累加10次未获得锁，打不过别的线程，下次再试试
                try {
                    long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                    Thread.sleep(sleepMillis);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return flag;
    }
}
