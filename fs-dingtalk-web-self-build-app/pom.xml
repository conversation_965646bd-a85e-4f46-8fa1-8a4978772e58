<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>fs-open-feishu-gateway</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>fs-dingtalk-web-self-build-app</artifactId>
    <name>fs-dingtalk-web-self-build-app</name>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>outer-oa-connector-base</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-dao-support</artifactId>
            <version>1.0.4-wujw-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.facishare.open</groupId>-->
        <!--            <artifactId>fs-dingtalk-api</artifactId>-->
        <!--            <version>1.1-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!--订单通讯录服务-->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-order-contacts-proxy</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- Dubbo -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging-api</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Netty: Required by dubbo-->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>

        <!-- 接入dubbo监控 -->

        <!-- logback, JVM日志上报  -->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke.common</groupId>
                    <artifactId>fs-kafka-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 配置中心 -->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>rpc-trace</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke.common</groupId>
                    <artifactId>fs-kafka-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>core-filter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>

        <!-- Spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>

        <!-- cookie换取身份信息 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-active-session-manage-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- poi excel start -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.0.0</version>
        </dependency>
        <!-- poi excel end -->

        <dependency>
            <groupId>com.taobao.top</groupId>
            <artifactId>lippi-oapi-encrpt</artifactId>
            <version>dingtalk-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/lippi-oapi-encrpt.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.taobao.top</groupId>
            <artifactId>top-api-sdk-dev</artifactId>
            <version>ding-open-mc-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/top-api-sdk-dev-ding-open-mc-20180719.091043-1.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-user-login-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.msg</groupId>
            <artifactId>fs-message-external-platform-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-restful-common</artifactId>
        </dependency>

        <!-- xss -->
        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder</artifactId>
            <version>1.2.1</version>
        </dependency>

        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder-jsp</artifactId>
            <version>1.2.1</version>
        </dependency>
        <!-- 处理xss攻击-->
        <!-- https://mvnrepository.com/artifact/org.owasp.esapi/esapi -->
        <dependency>
            <groupId>org.owasp.esapi</groupId>
            <artifactId>esapi</artifactId>
            <version>2.2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>outer-oa-connector-common-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke.common</groupId>
                    <artifactId>fs-kafka-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-kafka-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>5.8.5</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.facishare.open</groupId>-->
        <!--            <artifactId>fs-dingtalk-common</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->


        <!-- fs-dingtalk-provider pom.xml-->
        <!--        <dependency>-->
        <!--            <groupId>com.facishare.open</groupId>-->
        <!--            <artifactId>fs-dingtalk-api</artifactId>-->
        <!--            <version>1.1-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.facishare.open</groupId>-->
        <!--            <artifactId>fs-dingtalk-common</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20160810</version>
        </dependency>

        <!-- 开平通讯录 -->
        <!--        <dependency>-->
        <!--            <groupId>com.facishare.open</groupId>-->
        <!--            <artifactId>fs-addressbook-api</artifactId>-->
        <!--            <version>0.0.8-SNAPSHOT</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>fs-open-common-result</artifactId>-->
        <!--                    <groupId>com.facishare.open</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!-- 北研通讯录-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>i18n-util</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-restful-common</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-uc-api</artifactId>
        </dependency>

        <!-- 开平消息接口 -->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-api</artifactId>
        </dependency>

        <!--应用中心api-->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-app-center-api</artifactId>
            <version>1.0.54-SNAPSHOT</version>
        </dependency>

        <!-- Ok HttpClient -->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>http-spring-support</artifactId>
        </dependency>

        <!-- mybatis -->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke.common</groupId>
                    <artifactId>fs-kafka-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.36</version>
        </dependency>

        <!-- JUnit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-storage</artifactId>
            <version>0.0.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jedis-spring-support</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.2.0</version>
        </dependency>

        <!-- 导入神策系统数据埋点依赖 -->
        <dependency>
            <groupId>com.fxiaoke.cloud</groupId>
            <artifactId>datapersist</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
        </dependency>

        <!--webhook 消息分发-->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>message-send-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--        &lt;!&ndash; 接入redis &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.github.colin-lee</groupId>-->
        <!--            <artifactId>jedis-spring-support</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.facishare.open</groupId>-->
        <!--            <artifactId>fs-kiscloud-api</artifactId>-->
        <!--            <version>${kiscloud-api.version}</version>-->
        <!--        </dependency>-->

        <!--服务通数据源接口 -->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-eservice-rest-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-eservice-base</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <!--dubbo转rest -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-dubbo-rest-plugin</artifactId>
        </dependency>

        <!--        &lt;!&ndash; 引入crm rest接口&ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.fxiaoke</groupId>-->
        <!--            <artifactId>fs-crm-rest-api</artifactId>-->
        <!--            <version>1.2.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>oa-base-db-proxy</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-common-api</artifactId>
            <version>7.0.0-SNAPSHOT</version>
        </dependency>

        <!--拼音-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <!--xxl-job框架-->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-job-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mds-event</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.facishare</groupId>-->
        <!--            <artifactId>fs-common-mq</artifactId>-->
        <!--            <version>1.0.3-SNAPSHOT</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>config-core</artifactId>-->
        <!--                    <groupId>com.github.colin-lee</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>

        <!-- 添加pom依赖-->

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-plat-privilege-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--        引入重试机制-->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.2.2.RELEASE</version>
        </dependency>

        <!-- 查询license版本-->
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-license-api</artifactId>
        </dependency>
        <!-- 引入营销通，主要通讯录回调通知对方-->
        <dependency>
            <groupId>com.facishare.marketing</groupId>
            <artifactId>fs-marketing-outapi</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <!--hutool 工具集合引入-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.5</version>
        </dependency>

        <!-- 上传日志到kibana-->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>biz-log-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke.common</groupId>
                    <artifactId>fs-kafka-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--Mybatis查询分页-->
        <!--        <dependency>-->
        <!--            <groupId>com.github.pagehelper</groupId>-->
        <!--            <artifactId>pagehelper</artifactId>-->
        <!--            <version>5.1.6</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.fxiaoke</groupId>-->
        <!--            <artifactId>fs-other-rest-api</artifactId>-->
        <!--            <version>1.0.4-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>biz-log-proto</artifactId>
            <version>2.0.19</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.6.0</version>
        </dependency>

        <!-- ReflectASM  https://github.com/EsotericSoftware/reflectasm  -->
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>reflectasm</artifactId>
            <version>1.11.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.3.5</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <webResources>
                        <resource>
                            <directory>${project.basedir}/lib</directory>
                            <targetPath>WEB-INF/lib</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>