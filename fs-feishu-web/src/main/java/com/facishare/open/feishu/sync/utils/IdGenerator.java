package com.facishare.open.feishu.sync.utils;

import cn.hutool.core.util.IdUtil;
import com.facishare.open.feishu.syncapi.annotation.LogLevel;
import com.facishare.open.feishu.syncapi.enums.LogLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Slf4j
@Service
public class IdGenerator {

    /**
     * id生成器，
     * @deprecated 推荐直接使用 {@link com.fxiaoke.api.IdGenerator#get()}
     * @return
     */
    @LogLevel(value = LogLevelEnum.TRACE)
    @Deprecated
    public String get() {
        String idStr;
        try {
            idStr = com.fxiaoke.api.IdGenerator.get();
        } catch (Exception e) {
            idStr = IdUtil.nanoId();
        }
        return idStr;
    }
}
