package com.facishare.open.feishu.sync.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.utils.XorUtils;
import com.facishare.open.feishu.syncapi.arg.SendTextNoticeArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.outer.oa.connector.common.api.enums.DataTypeEnum;
import com.facishare.open.feishu.syncapi.service.NotificationService;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.result.BaseResult;
import com.facishare.open.feishu.syncapi.result.Token.AppAccessTokenResult;
import com.facishare.open.feishu.syncapi.result.Token.TenantAccessTokenResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component("feishuStoreAppManager")
public class FeishuStoreAppManager {

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @ReloadableProperty("feishu_app_info")
    private String appInfo;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;
    @Autowired
    private UrlManager urlManager;

    /**
     * 获取企业access token
     * @param appId
     * @param tenantKey
     * @return
     */
    public Result<String> getTenantAccessToken(String appId,String tenantKey){
        if(ConfigCenter.isOpenLog) {
            LogUtils.info("FeishuStoreAppManager.getTenantAccessToken,appId={},tenantKey={}", appId, tenantKey);
        }
        String key = "feishu-"+appId+"-"+tenantKey+"-tenantAccessToken";
        String tenantAccessToken = redisDataSource.getRedisClient().get(key);
        if (StringUtils.isNotEmpty(tenantAccessToken)) {
            if(ConfigCenter.isOpenLog) {
                LogUtils.info("FeishuStoreAppManager.getTenantAccessToken,appId={},tenantKey={},tenantAccess={}", appId, tenantKey, XorUtils.EncodeByXor(tenantAccessToken, ConfigCenter.XOR_SECRET_KEY));
            }
            return Result.newSuccess(tenantAccessToken);
        }
        String url = urlManager.getFeishuUrl(tenantKey, appId, FeishuUrlEnum.auth_v3_tenant_access_token.getUrl());
        HashMap<String, String> map = new HashMap<>();
        String appAccessToken = getAppAccessToken(appId).getData();
        map.put("app_access_token",appAccessToken);
        map.put("tenant_key",tenantKey);
        Map<String,String> headerMap = new HashMap<>();
        TenantAccessTokenResult result;
        if(ConfigCenter.isOpenLog) {
            String postResult = proxyHttpClient.postUrl(url, map, headerMap);
            result = JSONObject.parseObject(postResult, TenantAccessTokenResult.class);
        } else {
            result = proxyHttpClient.postUrl(url, map, headerMap, new TypeReference<TenantAccessTokenResult>() {
            });
        }

        if(result.getCode()!=0) {
            return new Result<>(result.getCode(),result.getMsg(),null);
        }
        String newTenantAccessToken = result.getTenant_access_token();
        LogUtils.info("FeishuStoreAppManager.getTenantAccessToken,appId={},tenantKey={},newTenantAccess={}", appId, tenantKey, XorUtils.EncodeByXor(appAccessToken + "_#_" + newTenantAccessToken, ConfigCenter.XOR_SECRET_KEY));

        redisDataSource.getRedisClient().set(key, newTenantAccessToken);
        //access token有效期是2小时，提前2分钟获取token
        long expire = result.getExpire() - 2 * 60L;
        if (expire < 0) {
            expire = result.getExpire();
        }
        redisDataSource.getRedisClient().expire(key, expire);
        return Result.newSuccess(newTenantAccessToken);
    }

    public void clearTenantAccessToken(String appId,String tenantKey) {
        String key = "feishu-"+appId+"-"+tenantKey+"-tenantAccessToken";
        redisDataSource.getRedisClient().del(key);
    }

    /**
     * 获取应用access token
     * @param appId
     * @return
     */
    public Result<String> getAppAccessToken(String appId) {
        if(ConfigCenter.isOpenLog) {
            LogUtils.info("FeishuStoreAppManager.getAppAccessToken,appId={}", appId);
        }
        String key = "feishu-" + appId + "-appAccessToken";
        String appTicketKey = "feishu-" + appId + "-appticket";

        String appAccessToken = redisDataSource.getRedisClient().get(key);
        if (StringUtils.isNotEmpty(appAccessToken)) {
            if(ConfigCenter.isOpenLog) {
                LogUtils.info("FeishuStoreAppManager.getAppAccessToken,appId={},tenantAccess={}", appId, XorUtils.EncodeByXor(appAccessToken, ConfigCenter.XOR_SECRET_KEY));
            }
            return Result.newSuccess(appAccessToken);
        }

        HashMap hashMap = JSON.parseObject(appInfo, HashMap.class);
        String appSecret = String.valueOf(hashMap.get(appId));
        String appTicket = redisDataSource.getRedisClient().get(appTicketKey);
        String url = urlManager.getFeishuUrl(null, appId, FeishuUrlEnum.auth_v3_app_access_token.getUrl());
        HashMap<String, String> map = new HashMap<>();
        map.put("app_id", appId);
        map.put("app_secret", appSecret);
        map.put("app_ticket", appTicket);
        Map<String,String> headerMap = new HashMap<>();
        AppAccessTokenResult result;
        if(ConfigCenter.isOpenLog) {
            String postResult = proxyHttpClient.postUrl(url, map, headerMap);
            result = JSONObject.parseObject(postResult, AppAccessTokenResult.class);
        } else {
            result = proxyHttpClient.postUrl(url, map, headerMap, new TypeReference<AppAccessTokenResult>() {
            });
        }
        log.info("FeishuStoreAppManager.getAppAccessToken,appId={},result={}",appId,result);
        if(result.getCode()!=0) {
            //告警suite_ticket失效
            if(result.getCode() == 10012) {
                log.info("FeishuStoreAppManager.getAppAccessToken.ticket is invalid");
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("飞书suite_ticket缓存失效告警"); //ignorei18n
                String msg = String.format("飞书suite_ticket缓存失效，请及时关注\nappId=%s\n返回结果=%s",appId, result.getMsg()); //ignorei18n
                arg.setMsg(msg);
                log.info("FeishuStoreAppManager.getAppAccessToken,arg={}", arg);
                notificationService.sendNotice(arg);
            }
            //上报
            oaConnectorDataManager.send(null, null, ChannelEnum.feishu.toString(),
                    DataTypeEnum.SUITE_TICKET.getDataType(), appTicket, null, String.valueOf(result.getCode()), result.getMsg());
            return new Result<>(result.getCode(),result.getMsg(),null);
        }
        redisDataSource.getRedisClient().set(key, result.getApp_access_token());
        //access token有效期是2小时，提前2分钟获取token
        long expire = result.getExpire() - 2 * 60L;
        if (expire < 0) {
            expire = result.getExpire();
        }
        redisDataSource.getRedisClient().expire(key, expire);
        //上报
        oaConnectorDataManager.send(null, null, ChannelEnum.feishu.toString(),
                DataTypeEnum.SUITE_TICKET.getDataType(), appTicket, result.getApp_access_token(), null, null);
        return Result.newSuccess(result.getApp_access_token());
    }

    public void clearAppAccessToken(String appId) {
        String key = "feishu-" + appId + "-appAccessToken";
        redisDataSource.getRedisClient().del(key);
    }

    /**
     * 重新推送 app_ticket
     * @param appId
     * @return
     */
    public boolean resentAppTicket(String appId) {
        HashMap hashMap = JSON.parseObject(appInfo, HashMap.class);
        String appSecret = String.valueOf(hashMap.get(appId));
        String feishuUrl = urlManager.getFeishuUrl(null, appId, FeishuUrlEnum.auth_v3_app_ticket_resend.getUrl());
        HashMap<String, String> map = new HashMap<>();
        map.put("app_id", appId);
        map.put("app_secret", appSecret);
        Map<String,String> headerMap = new HashMap<>();

        BaseResult baseResult = proxyHttpClient.postUrl(feishuUrl, map, headerMap, new TypeReference<BaseResult>() {
        });
        return baseResult.code==0;
    }

}
