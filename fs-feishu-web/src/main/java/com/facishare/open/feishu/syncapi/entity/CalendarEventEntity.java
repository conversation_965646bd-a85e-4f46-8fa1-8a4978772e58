package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_calendar_event_info")
public class CalendarEventEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 渠道
     */
    private ChannelEnum channel;
    /**
     * 纷享企业账号id
     */
    private String fsEa;
    /**
     * 外部企业账号id
     */
    private String outEa;
    /**
     * 纷享日程对象id
     */
    private String objectId;
    /**
     * 日程id
     */
    private String eventId;
    /**
     * 状态
     */
    private Integer status;

    private Date createTime;
    private Date updateTime;
}
