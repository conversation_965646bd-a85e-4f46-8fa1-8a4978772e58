package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.arg.InsertEmployeeBindArg;
import com.facishare.open.feishu.syncapi.arg.QueryEmployeeBindArg;
import com.facishare.open.feishu.syncapi.arg.UpdateEmployeeBindArg;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.info.EmployeeBindInfo;
import com.facishare.open.feishu.syncapi.model.*;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserUpdatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.model.info.EmployeesBindSyncInfo;
import com.facishare.open.feishu.syncapi.model.info.FsEmployeeDetailInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.info.EmployeeBindModel;
import com.facishare.open.outer.oa.connector.common.api.info.PageModel;
import com.facishare.organization.api.model.employee.EmployeeDto;

import java.util.List;
import java.util.Map;

public interface EmployeeBindService {
        /**
         * 获取应用安装人信息
         * 
         * @param appId
         * @param tenantKey
         * @return
         */
        Result<UserData.User> getAppInstallerInfo(String appId, String tenantKey);

        /**
         * 获取应用管理员
         * 
         * @param appId
         * @param tenantKey
         * @return
         */
        Result<UserData.User> getAppAdminInfo(String appId, String tenantKey);

    /**
     * 搜索已绑定员工信息
     * @param content
     * @return
     */
    Result<PageModel<List<EmployeeBindModel>>> searchBind(String fsEa, String outEa, String content, Integer pageSize);

    /**
     * 搜索未绑定员工信息
     * @param content
     * @return
     */
    Result<PageModel<List<EmployeeBindModel.FsEmployee>>> searchUnBind(String fsEa, String outEa, String content, Integer pageSize);


    /**
     * 查询帐号绑定信息
     * @param fsEa 纷享EA
     * @return
     */
    Result<PageModel<List<EmployeeBindModel>>> queryBind(String fsEa, String outEa, Integer pageSize);

    /**
     * 查询未绑定的纷享员工列表
     * @param fsEa 纷享EA
     * @param pageSize
     * @return
     */
    Result<PageModel<List<EmployeeBindModel.FsEmployee>>> queryFsUnbind(String fsEa, String outEa, Integer pageSize);

        /**
         * 查询CRM应用可见范围未绑定的飞书员工列表
         * 
         * @param fsEa     纷享EA
         * @param pageSize
         * @return
         */
        Result<List<EmployeeBindModel.OutEmployee>> queryOutUnbind(String fsEa, String outEa, String searchText,
                        Integer pageSize);

        /**
         * 查询飞书CRM应用可见范围内的员工
         * 
         * @param fsEa 纷享EA
         * @return
         */
        Result<EmployeeUnBindModel> queryUnbind(String fsEa, String outEa, Integer pageSize);

        /**
         * 查询飞书CRM应用可见范围内的员工
         * 
         * @param fsEa 纷享EA
         * @return
         */
        Result<List<EmployeeBindModel.OutEmployee>> queryOutEmployeeList(String fsEa, String outEa);

        /**
         * 解除员工绑定
         * 
         * @param fsEa 纷享EA
         * @return
         */
        Result<Boolean> unBind(String fsEa, String fsUserId, String outUserId);

        /**
         * 重新绑定员工
         * 
         * @param fsEa 纷享EA
         * @return
         */
        Result<Boolean> reBind(String fsEa, String fsUserId, String outUserId, String outEa);

        /**
         * 批量绑定员工
         * 
         * @param fsEa 纷享EA
         * @return
         */
        Result<List<EmployeeBindModel2>> batchBind(String fsEa, List<EmployeeBindModel2> employeeList, String outEa);

        /**
         * 批量解除员工绑定
         * 
         * @param fsEa 纷享EA
         * @return
         */
        Result<List<EmployeeBindModel2>> batchUnBind(String fsEa, List<EmployeeBindModel2> employeeList);

        /**
         * 批量更新员工绑定状态
         * 
         * @param outEa
         * @param outUserId
         * @param bindStatus
         * @return
         */
        Result<Void> updateEmployeeStatus(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outUserId,
                        BindStatusEnum bindStatus);

        /**
         * 新增或者更新员工
         * 
         * @param event 员工更新事件
         * @return
         */
        Result<Void> addOrUpdateEmployee(FeishuEventModel2.EventModelHeader header,
                        FeishuContactUserUpdatedV3Event event, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity);

        Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo(Integer ei, Integer userId);

        Result<Void> uploadEmployeesBindSyncFile(List<EmployeesBindSyncInfo> employeesBindSyncInfos);

        /**
         * 缓存员工信息
         * 
         * @param fsEa        纷享ea
         * @param employeeDto fs员工信息
         * @return
         */
        Result<List<EmployeeDto>> fsEmployeeInfoCache(String fsEa, EmployeeDto employeeDto);

        Result<Void> autoBindBySettingUnique(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String appId,
                        ContactScopeModel contactScopeModel);

        Result<Integer> insertEmployeeBind(String channel, InsertEmployeeBindArg arg);

        Result<Integer> updateEmployeeBind(String channel, UpdateEmployeeBindArg arg);

        Result<EmployeeBindInfo> queryEmployeeBind(String channel, QueryEmployeeBindArg arg);

        Result<EmployeeBindEntity> queryEmpData(ChannelEnum channel, String fsEa, String fsUserId, String outEa,
                        String outUserId);

        Result<Integer> insertEmpData(EmployeeBindEntity entity);

        Result<Void> createEmployeeInfo(String outEa, String appId, String outUserId, String fsEa);

        Result<Void> autoBindEmpByCrmEmployeeNumber(String fsEa, String outEa, List<EmployeeDto> employeeDtoList,
                        String autoField);

        Result<List<EmployeeBindInfo>> queryEmployeeBindListByOutData(ChannelEnum channel, String outEa,
                        String outUserId);

        Result<Map<String, String>> filterEmployeeAutoBind(Integer tenantId, String objectApiName, List<String> dataValues,
                                                      String objectField);
}
