package com.facishare.open.feishu.syncapi.service.whatsapp;

import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappCreateTemplate;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappDeleteTemplate;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappGetTemplate;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappUpdateTemplate;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappCreateTemplateResult;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappDeleteTemplateResult;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappGetTemplateResult;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappUpdateTemplateResult;

public interface WhatsappTemplateService {
    Result<WhatsappGetTemplateResult> getTemplateData(WhatsappGetTemplate getTemplate);

    Result<WhatsappCreateTemplateResult> createTemplateData(WhatsappCreateTemplate createTemplate);

    Result<WhatsappUpdateTemplateResult> updateTemplateData(WhatsappUpdateTemplate updateTemplate);

    Result<WhatsappDeleteTemplateResult> deleteTemplateData(WhatsappDeleteTemplate deleteTemplate);
}
