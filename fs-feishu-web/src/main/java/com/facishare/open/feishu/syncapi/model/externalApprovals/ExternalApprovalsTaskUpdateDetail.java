package com.facishare.open.feishu.syncapi.model.externalApprovals;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExternalApprovalsTaskUpdateDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "message_id")
    private String messageId;
    @JSONField(name = "status")
    private String status;
    @JSONField(name = "status_name")
    private String statusName;
    @JSONField(name = "detail_action_name")
    private String detailActionName;
    @JSONField(name = "i18n_resources")
    private String i18nResources;
}
