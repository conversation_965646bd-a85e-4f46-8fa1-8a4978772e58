package com.facishare.open.oa.base.dbproxy.mongo.dao;

import com.facishare.open.oa.base.dbproxy.mongo.document.FsUserInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.store.FsUserInfoMongoStore;
import com.google.common.collect.Lists;
import com.mongodb.MongoException;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;

/**
 * 纷享人员详情操作mongo的dao类封装
 * <AUTHOR>
 * @date 2023/12/06
 */
@Slf4j
@Repository
public class FsUserInfoMongoDao {
    public static final String fs_id = "_id";
    public static final String fs_fsEa = "fsEa";
    public static final String fs_fsUserId = "fsUserId";
    public static final String fs_fsStatus = "status";
    public static final String fs_fsUserInfo = "fsUserInfo";
    public static final String fs_createTime = "createTime";
    public static final String fs_updateTime = "updateTime";
    public static final String fs_appId = "updateTime";

    private final FsUserInfoMongoStore store;

    public FsUserInfoMongoDao(FsUserInfoMongoStore store) {
        this.store = store;
    }

    private List<ObjectId> convertObjectIds(Collection<String> ids) {
        List<ObjectId> objIds = ids.stream()
                .filter(ObjectId::isValid)
                .map(ObjectId::new).collect(Collectors.toList());
        return objIds;
    }

    private static ObjectId getObjId(String id) {
        return new ObjectId(id);
    }

    private int update(List<Bson> filters, List<Bson> updates) {
        updates.add(currentDate(fs_updateTime));
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = store.getOrCreateCollection().updateOne(filter, update);
        return (int) updateResult.getModifiedCount();
    }


    public List<FsUserInfoDoc> listByTenantId(Integer offset, Integer limit) {
        List<FsUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find().limit(limit).skip(offset).into(res);
        return res;
    }

    /**
     * 忽略结果
     *
     * @param outUserInfoDoc
     * @return 1 成功 0 失败
     */

    public int insertIgnore(FsUserInfoDoc outUserInfoDoc) {
        MongoCollection<FsUserInfoDoc> collection = store.getOrCreateCollection();
        try {
            collection.insertOne(outUserInfoDoc);
        } catch (MongoException mongoException) {
            log.error("sync data insert exception", mongoException);
            return 0;
        }
        return 1;
    }


    public FsUserInfoDoc getById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        FsUserInfoDoc fsUserInfoDoc = store.getOrCreateCollection().find(eq(new ObjectId(id))).limit(1).first();
        return fsUserInfoDoc;
    }


    public FsUserInfoDoc getSimpleById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        FsUserInfoDoc fsUserInfoDoc = store.getOrCreateCollection()
                .find(eq(new ObjectId(id)))
                .limit(1).first();
        return fsUserInfoDoc;
    }


    public List<FsUserInfoDoc> listByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(fs_id, objIds);
        List<FsUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }


    public List<FsUserInfoDoc> listSimpleByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(fs_id, objIds);
        List<FsUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public void save(FsUserInfoDoc outUserInfoDoc) {
        log.info("FsUserInfoMongoDao.save,outUserInfoDoc={}", outUserInfoDoc);
        Bson filter = and(eq(fs_id, outUserInfoDoc.getId()));
        store.getOrCreateCollection().replaceOne(filter, outUserInfoDoc, new ReplaceOptions().upsert(true));
    }

    /**
     * 批量插入或替换记录
     *
     * @param docList
     * @return
     */
    public BulkWriteResult batchReplace(Collection<FsUserInfoDoc> docList) {
        log.info("FsUserInfoMongoDao.batchReplace,docList={}", docList);
        List<WriteModel<FsUserInfoDoc>> request = new ArrayList<>();
        for (FsUserInfoDoc doc : docList) {
            Bson filter = and(eq(fs_fsEa, doc.getFsEa()), eq(fs_fsUserId, doc.getFsUserId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("FsUserInfoMongoDao.batchReplace,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }


    /**
     * 批量更新，无则不插入。
     *
     * @param docList
     * @return insert count
     */
    public BulkWriteResult batchUpdate(Collection<FsUserInfoDoc> docList) {
        log.info("FsUserInfoMongoDao.batchUpdate,docList={}", docList);
        List<WriteModel<FsUserInfoDoc>> request = new ArrayList<>();
        for (FsUserInfoDoc doc : docList) {
            Bson filter = and(eq(fs_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("FsUserInfoMongoDao.batchUpdate,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }

    public List<FsUserInfoDoc> queryUserInfos(String fsEa, Integer status) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(fs_fsEa, fsEa));
        if(ObjectUtils.isNotEmpty(status)) {
            filters.add(Filters.eq(fs_fsStatus, status));
        }
        Bson filter = Filters.and(filters);
        List<FsUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public List<FsUserInfoDoc> queryUserInfosByNotIds(String fsEa, Integer status, List<Integer> userIds) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(fs_fsEa, fsEa));
        if(ObjectUtils.isNotEmpty(status)) {
            filters.add(Filters.eq(fs_fsStatus, status));
        }
        if(CollectionUtils.isNotEmpty(userIds)) {
            filters.add(Filters.nin(fs_fsUserId, userIds));
        }
        Bson filter = Filters.and(filters);
        List<FsUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public List<FsUserInfoDoc> queryUserInfosByIds(String fsEa, Integer status, List<Integer> userIds) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(fs_fsEa, fsEa));
        if(ObjectUtils.isNotEmpty(status)) {
            filters.add(Filters.eq(fs_fsStatus, status));
        }
        if(CollectionUtils.isNotEmpty(userIds)) {
            filters.add(Filters.in(fs_fsUserId, userIds));
        }
        Bson filter = Filters.and(filters);
        List<FsUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public DeleteResult deleteUserInfo(FsUserInfoDoc doc) {
        Bson filter = and(eq(fs_id, doc.getId()));
        return store.getOrCreateCollection().deleteMany(filter);
    }

    public DeleteResult deleteUserInfoByUserId(String fsEa, Integer fsUserId) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(fs_fsEa, fsEa), Filters.eq(fs_fsUserId, fsUserId));
        Bson filter = Filters.and(filters);
        return store.getOrCreateCollection().deleteOne(filter);
    }

    public DeleteResult deleteNotInCollectionDocs(String fsEa, Collection<FsUserInfoDoc> docList) {
        Bson filter = deleteNotInCollectionDocsBson(fsEa, docList);
        return store.getOrCreateCollection().deleteMany(filter);
    }

    private Bson deleteNotInCollectionDocsBson(String fsEa, Collection<FsUserInfoDoc> docList) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(fs_fsEa, fsEa));
        List<Bson> neOutUserFilters = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(docList)) {
            for(FsUserInfoDoc doc : docList) {
                List<Bson> eventTypeFilters = Lists.newArrayList(Filters.eq(fs_fsUserId, doc.getFsUserId()));
                Bson filterBson = Filters.and(eventTypeFilters);
                neOutUserFilters.add(filterBson);
            }
            filters.add(Filters.nor(neOutUserFilters));
        }
        return Filters.and(filters);
    }

    public void deleteIndex(String index) {
        store.getOrCreateCollection().dropIndex(index);
    }

    public Long countDocuments(String fsEa) {
        Bson filter = and(eq(fs_fsEa, fsEa));
        return store.getOrCreateCollection().countDocuments(filter);
    }

    public void addIndex() {
        List<IndexModel> indexList = Lists.newArrayList();
        Bson fsEaStatusBson = Indexes.compoundIndex(
                Indexes.ascending(FsDepartmentInfoMongoDao.fs_fsEa),
                Indexes.ascending(FsDepartmentInfoMongoDao.fs_fsStatus));
        indexList.add(new IndexModel(fsEaStatusBson, new IndexOptions()
                .name("index_fsEa_status")
                .background(true)));
        List<String> indexes = store.getOrCreateCollection().createIndexes(indexList);
        System.out.println(indexes);
    }
}
