package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外部OA人员数据表查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OuterOaEmployeeDataParams {
    /**
     * 外部企业账号
     */
    private String outEa;
    /**
     * 外部企业账号
     */
    private String fsEa;


    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部用户id
     */
    private String outUserId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 部门id
     */
    private String outDeptId;

    /**
     * 动态查询字段1
     */
    private String text1;

    /**
     * 动态查询字段2
     */
    private String text2;

    /**
     * 动态查询字段3
     */
    private String text3;

    /**
     * 动态查询字段4
     */
    private String text4;
}