//package service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.model.connector.*;
//import com.facishare.open.ding.api.result.ConnectorResult;
//import com.facishare.open.ding.api.service.SyncBizDataService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorSyncObjectService;
//import com.facishare.open.ding.cloud.constants.Constant;
//import com.facishare.open.ding.common.result.Result;
//import com.google.common.collect.Lists;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.StringUtils;
//
//public class ConnectorSyncObjectServiceTest extends BaseAbstractTest {
//    @Autowired
//    private ConnectorSyncObjectService connectorSyncObjectService;
//    @Autowired
//    private SyncBizDataService syncBizDataService;
//
//    @Test
//    public void syncProduct() {
//        ProductModel productModel = new ProductModel();
//        productModel.setDdDataCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        productModel.setDdDataCreateAppType("premium_microapp");
//        productModel.setDdDataCreateAppId("premium_microapp@61790");
//        productModel.setOperateUserId("manager5826");
//        productModel.getData().setName("ERP商品005");
//        productModel.getData().setCode("SP00005");
//        productModel.getData().setBaseUnitCode("Pcs");
//
//        String json = "{\"ddDataGmtCreate\":1628583551111,\"ddDataCreateAppType\":\"premium_microapp\",\"ddDataModifiedAppId\":\"premium_microapp@61790\",\"ddDataAction\":\"add\",\"data\":{\"code\":\"SP00001\",\"baseUnitCode\":\"个\",\"name\":\"ERP商品001\",\"unitList\":[],\"barcode\":\"\",\"priceList\":[{\"unitCode\":\"个\",\"costPrice\":\"0\",\"retailPrice\":\"0\"}]},\"ddDataModifiedAppType\":\"premium_microapp\",\"operateUserId\":\"manager5826\",\"ddDataGmtModified\":1628583551111,\"ddDataCorpId\":\"ding3959290e81fe7b844ac5d6980864d335\",\"ddDataCreateAppId\":\"premium_microapp@61790\",\"ddDataModelId\":\"EM-10149F1A986F0B1738B3000H\"}";
//
//        ConnectorResult result = connectorSyncObjectService.syncProduct("ding0c8ee222b9a38265f2c783f7214b6d69",
//                81243,
//                Constant.APP_ID,
//                JSONObject.toJSONString(productModel));
//        System.out.println(result);
//    }
//
//    @Test
//    public void syncWarehouse() {
//        WarehouseModel warehouseModel = new WarehouseModel();
//        warehouseModel.setDdDataCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        warehouseModel.setDdDataCreateAppId(Constant.APP_ID+"");
//        warehouseModel.setOperateUserId("-10000");
//        warehouseModel.getData().setName("连接器测试仓库2");
//        warehouseModel.getData().setCode("connector-test-warehouse2");
//        warehouseModel.getData().setRemark("test");
//        ConnectorResult result = connectorSyncObjectService.syncWarehouse("ding0c8ee222b9a38265f2c783f7214b6d69",
//                81243,
//                Constant.APP_ID,
//                JSONObject.toJSONString(warehouseModel));
//        System.out.println(result);
//    }
//
//    @Test
//    public void syncReceipt() {
//        String jsonData = "{\"ddDataGmtCreate\":*************,\"ddDataCreateAppType\":\"premium_microapp\",\"ddDataModifiedAppId\":\"premium_microapp@61790\",\"ddDataAction\":\"update\",\"data\":{\"recvType\":1,\"amount\":341,\"code\":\"XSCK-********-00001\",\"empCode\":\"013065185604842061\",\"empName\":\"李四\",\"customerCode\":\"********-000004\",\"billDate\":*************,\"customerName\":\"CRMtest004\",\"settleAccount\":\"现金\",\"orderSettlementList\":[{\"orderCode\":\"********-000004\",\"settlementAmount\":176},{\"orderCode\":\"********-000004\",\"settlementAmount\":165}]},\"ddDataModifiedAppType\":\"premium_microapp\",\"operateUserId\":\"manager5826\",\"ddDataGmtModified\":*************,\"ddDataCorpId\":\"ding3959290e81fe7b844ac5d6980864d335\",\"ddDataCreateAppId\":\"premium_microapp@61790\",\"ddDataModelId\":\"EM-10149F1A4D2D0B1738B3000R\"}";
//        ReceiptModel receiptModel = JSONObject.parseObject(jsonData,ReceiptModel.class);
////        receiptModel.setDdDataCorpId("ding3959290e81fe7b844ac5d6980864d335");
////        receiptModel.setDdDataCreateAppId(Constant.APP_ID+"");
////        receiptModel.setOperateUserId("-10000");
////        receiptModel.getData().setCode("XSCK-********-00001");
////        receiptModel.getData().setCustomerCode("6113309930c2fd000183496b");
////        receiptModel.getData().setBillDate(System.currentTimeMillis());
////        receiptModel.getData().setEmpCode("013065185604842061");
////        receiptModel.getData().setRemark("test2");
////
////        ReceiptModel.OrderSettlementModel orderSettlementModel = new ReceiptModel.OrderSettlementModel();
////        orderSettlementModel.setOrderCode("6113369930c2fd000183bc7e");
////        orderSettlementModel.setOrderPaymentAmount("10.00");
////
////        receiptModel.getData().getOrderSettlementList().add(orderSettlementModel);
//
//        ConnectorResult result = connectorSyncObjectService.syncReceipt("ding3959290e81fe7b844ac5d6980864d335",
//                82801,
//                Constant.APP_ID,
//                jsonData);
//        System.out.println(result);
//    }
//
//    @Test
//    public void syncCustomer() {
//        CustomerModel customerModel = new CustomerModel();
//        customerModel.setDdDataCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        customerModel.setDdDataCreateAppId(Constant.APP_ID+"");
//        customerModel.setOperateUserId("-10000");
//        customerModel.getData().setName("贝贝测试客户1002");
//        customerModel.getData().setCode("customer_no_100");
//        customerModel.getData().setEmpCode("013065185604842061");
//        customerModel.getData().setRemark("test remark");
//
//        CustomerModel.Address address = new CustomerModel.Address();
//        address.setProvince("广东省");
//        address.setCity("深圳市");
//        address.setDistrict("南山区");
//        address.setDetailAddress("大冲国际中心22楼2201_2");
//        customerModel.getData().setAddress(address);
//
//        CustomerModel.ContactPerson contactPerson = new CustomerModel.ContactPerson();
//        contactPerson.setName("小贝2");
//
//        CustomerModel.Address address2 = new CustomerModel.Address();
//        address2.setProvince("广东省");
//        address2.setCity("深圳市");
//        address2.setDistrict("南山区");
//        address2.setDetailAddress("大冲国际中心22楼2201A_2");
//        contactPerson.setAddress(address2);
//
//        CustomerModel.ContactType mobile = new CustomerModel.ContactType();
//        mobile.setType("mobile");
//        mobile.setValue("18926584793");
//
//        CustomerModel.ContactType phone = new CustomerModel.ContactType();
//        phone.setType("phone");
//        phone.setValue("0755-1234567");
//
//        CustomerModel.ContactType email = new CustomerModel.ContactType();
//        email.setType("email");
//        email.setValue("<EMAIL>");
//
//        contactPerson.getContactTypeList().addAll(Lists.newArrayList(mobile,phone,email));
//
//        customerModel.getData().getContactPersonList().add(contactPerson);
//
//        ConnectorResult result = connectorSyncObjectService.syncCustomer("ding0c8ee222b9a38265f2c783f7214b6d69",
//                81243,
//                Constant.APP_ID,
//                JSONObject.toJSONString(customerModel));
//        System.out.println(result);
//    }
//
//    @Test
//    public void syncCustomerCategory() {
//        CustomerCategoryModel customerCategoryModel = new CustomerCategoryModel();
//        customerCategoryModel.setDdDataCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        customerCategoryModel.setDdDataCreateAppId(Constant.APP_ID+"");
//        customerCategoryModel.setOperateUserId("-10000");
//        customerCategoryModel.getData().setCode("connector-test-customerCategory1");
//        customerCategoryModel.getData().setName("客户分类1");
//        customerCategoryModel.getData().setRemark("test2");
//
//        ConnectorResult result = connectorSyncObjectService.syncCustomerCategory("ding0c8ee222b9a38265f2c783f7214b6d69",
//                81243,
//                Constant.APP_ID,
//                JSONObject.toJSONString(customerCategoryModel));
//        System.out.println(result);
//    }
//
//    @Test
//    public void syncProductBrand() {
//        ProductBrandModel model = new ProductBrandModel();
//        model.setDdDataCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        model.setDdDataCreateAppId(Constant.APP_ID+"");
//        model.setOperateUserId("-10000");
//        model.getData().setCode("connector-test-productBrand1");
//        model.getData().setName("产品品牌1");
//        model.getData().setRemark("test2");
//
//        ConnectorResult result = connectorSyncObjectService.syncProductBrand("ding0c8ee222b9a38265f2c783f7214b6d69",
//                81243,
//                Constant.APP_ID,
//                JSONObject.toJSONString(model));
//        System.out.println(result);
//    }
//    @Test
//    public void testServcie(){
//        Result<String> result = syncBizDataService.queryToken("dingfeca3fa3352c7d4ca39a90f97fcb1e09", "16740006");
//        if(!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
//            System.out.println(result);
//        }
//        System.out.println(result);
//    }
//
//    @Test
//    public void syncSalesOrder() {
//        SalesOrderModel model = new SalesOrderModel();
//        model.setDdDataCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        model.setDdDataCreateAppId(Constant.APP_ID+"");
//        model.setOperateUserId("-10000");
//        model.getData().setName("测试订单100");
//        model.getData().setCode("test_order_100");
//        model.getData().setBillDate(System.currentTimeMillis());
//        model.getData().setRemark("test");
//        model.getData().setCustomerCode("customer_no_100");
//
//        SalesOrderModel.ProductModel productModel = new SalesOrderModel.ProductModel();
//        productModel.setName("测试产品100");
//        productModel.setProductCode("SP00005");
//        productModel.setPrice("20");
//        productModel.setTaxPrice("18");
//        productModel.setQuantity("5");
//        productModel.setUnitCode("Pcs");
//        productModel.setDiscountRate("90");
//        productModel.setAmount("90");
//
//        model.getData().getProductList().add(productModel);
//        ConnectorResult result = connectorSyncObjectService.syncSalesOrder("ding0c8ee222b9a38265f2c783f7214b6d69",
//                81243,
//                Constant.APP_ID,
//                JSONObject.toJSONString(model));
//        System.out.println(result);
//    }
//}
