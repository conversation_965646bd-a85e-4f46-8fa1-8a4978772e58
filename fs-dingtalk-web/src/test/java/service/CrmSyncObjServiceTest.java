package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.arg.NeedSyncDataArg;
import com.facishare.open.ding.api.model.NeedSyncDataModel;
import com.facishare.open.ding.api.result.CrmSyncObjResult;
import com.facishare.open.ding.api.service.CrmSyncObjService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class CrmSyncObjServiceTest extends BaseAbstractTest {
    @Autowired
    private CrmSyncObjService crmSyncObjService;

    @Test
    public void queryNeedSyncDataTest() {
        NeedSyncDataModel needSyncDataModel = new NeedSyncDataModel();
        NeedSyncDataArg needSyncDataArg=new NeedSyncDataArg();
        needSyncDataArg.setDirection(0);
        needSyncDataArg.setIsInit(0);
        needSyncDataModel.setNeedSyncDataArg(needSyncDataArg);
        int num = 1;
        CrmSyncObjResult result;
         {
            needSyncDataModel.setPageNum(((num++)-1)*5);
            needSyncDataModel.setPageSize(5);
            result = crmSyncObjService.queryNeedSyncData(needSyncDataModel);
            System.out.println(result);
        } while (result.getIsHasNextPage());

    }
}
