package com.facishare.open.ding.api.enums;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/24 15:18
 */
public enum OrderEventEnum {
    /** 新购 **/
    BUY("BUY"),

    /** 续费 **/
    RENEW("RENEW"),

    /** 升级 **/
    UPGRADE("UPGRADE"),

    /** 续费升配**/

    RENEW_UPGRADE("RENEW_UPGRADE"),

    /** 续费降配 **/
    RENEW_DEGRADE("RENEW_DEGRADE")
    ;

    private String type;

    OrderEventEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static OrderEventEnum getEventEmum(String type){
        for (OrderEventEnum e : OrderEventEnum.values()){
            if(e.type.equals(type)){
                return e;
            }
        }
        return null;
    }
}
