package com.facishare.open.ding.provider.dss.entity;

import com.github.mybatis.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;
@Data

public class DssSYncDataMappingEntity extends BaseEntity implements Serializable {

        @Id
        /** 关联关系主键id */
        private String id;
        private String tenantId;
        /** 目标企业主键id */
        private String sourceTenantId;
        /** 源企业主对象apiName */
        private String sourceObjectApiName;
        /** 源数据id */
        private String sourceDataId;
        /** 源数据名称 */
        private String sourceDataName;
        /** 目标主对象apiName */
        private String destObjectApiName;
        /** 目标企业id */
        private String destTenantId;
        /** 目标数据id */
        private String destDataId;
        /** 目标数据名称 */
        private String destDataName;
        /** 目标数据是否成功被创建 */
        private Boolean isCreated;
        /** 逻辑删除 */
        private Boolean isDeleted;
        /** 最近一次同步数据id */
        private String lastSyncDataId;
        /** 日志备注 */
        private String remark;

        private Integer lastSyncStatus;
        /** 最近一次同步版本号 */
        private Long lastSourceDataVserion;
        /** 主对象源数据id */
        private String masterDataId;
    }
