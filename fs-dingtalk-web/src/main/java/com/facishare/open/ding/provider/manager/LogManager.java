package com.facishare.open.ding.provider.manager;

import com.facishare.open.ding.api.enums.OperationStatusEnum;
import com.facishare.open.ding.api.enums.OperationTypeEnum;
import com.facishare.open.ding.api.enums.SyncLogStatusEnum;
import com.facishare.open.ding.api.enums.SyncTypeEnum;
import com.facishare.open.ding.provider.entity.DingSyncApi;
import com.facishare.open.ding.provider.entity.LogWriteVo;
import com.facishare.open.ding.provider.mongodb.entity.DingSyncLogDO;
import com.facishare.open.ding.provider.utils.DynamicParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2021/2/24 19:24
 * @Version 1.0
 */
@Service
public class LogManager {

    private static final Integer OPERATOR = -10000;

    @Autowired
    private DingSyncLogManager dingSyncLogManager;

    public void writeLog(LogWriteVo vo,String apiObj){
        DingSyncApi syncApi = new DingSyncApi();
        syncApi.setApiName(apiObj);
        syncApi.setSyncDirection(2);
        DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(vo.getEi(), syncApi, vo.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), vo.getOperateType());
        String detail = "dingding：create apiName[{" + apiObj + "}], [" + vo.getName() + "]";
        String content="";
        content = String.format("[%s] [%s]", vo.getName(),vo.getMessage());

        DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, vo.getStatus(), 1, 0, 1, vo.getStatus());
        dingSyncLogManager.logDingSync(kcSyncLogDO);
    }

}
