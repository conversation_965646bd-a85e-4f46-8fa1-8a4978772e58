package com.facishare.open.ding.common.result;

/**
 * Created by system on 2018/3/29.
 */
public enum ResultCode {

	/** 成功 **/
	SUCCESS(0, "success", "成功"),

	/** 系统繁忙 */
	SERVER_BUSY(-1, "server is busy", "系统繁忙"),

	/** 系统错误 */
	SYSTEM_ERROR(-2, "system error", "系统错误"),

	NOT_APP_MANAGER(-3, "not app manager", "用户不是应用管理员"),

	NOT_CLOUD_MANAGER(-4, "not cloud manager", "Cloud用户名或密码不正确"),

	ENTERPRISE_NOT_BIND(-5, "the enterprise not bind", "企业未绑定或不存在"),

	CLIENT_POST_FAILED(-8,"client post error","中转客户端post请求钉钉openapi失败"),


	PARAMS_ERROR(-101, "params error", "参数错误"),

	OUT_OF_LIMIT(-102, "query count is out of limit, maximum is 1000", "超出批量查询最大数量1000"),

	CONFIGURATION_NOT_EXIST(-201, "configuration not exist", "对接配置不存在"),

	CONFIGURATION_ALREADY_EXIST(-202, "configuration already exist", "对接配置已存在"),

	SYNC_TASK_ALREADY_RUNNING(-203, "sync task is already running", "同步任务正在运行，请等待结果通知"),

	NOT_SUPPORT_MANUAL_SYNC(-204, "not support manual sync", "不支持手动同步"),

	ALREADY_BIND_ACCOUNT(-206, "already bind account", "已存在绑定账号"),

	NOT_BIND_EMP(-207, "emp not bind", "员工未绑定"),

	CLOUD_HAS_CONNECTED(-300,"cloud has connected","已经有企业与该cloud已经建立连接"),

	DING_SERVER_EXCEPTION(-301, "dingding server exception", "钉钉接口异常"),

	DING_REGIST_FAILED(-304, "regist call back failed", "注册回调业务接口失败,请检查参数"),

	DING_CONNECT_PARAM_ERROR(-305,"dingding params error","钉钉appKey或appSecret或clientIp错误"),

	SAVE_ENTERPRISE_ERROR(-306,"save enterprise failed","保存企业映射关系失败"),

	DEPT_LIST_ERROR(-308,"query dingding dept list failed","查询钉钉部门列表失败"),

	INIT_DING_EMP(-310, "dingding employee is initing", "正在初始化钉钉用户，请稍后"),

	GET_DING_EMP_FAILED(-312, "get dingding user failed", "查询钉钉员工失败"),

	TO_USER_EMPTY(-314,"message receiver is empty","消息接收人员为空"),

	DING_CORPID_ERROR(-316,"dingding params error","钉钉corpId或clientIp错误"),

	TOKEN_NOT_EXIST(-401, "token not exist", "token不存在"),

	EMP_HAS_BIND(-601,"employee has been binded","该纷享职员已被绑定"),

	FIELD_INIT_ERROR(-602,"bind field failed","字段初始化失败"),

	GET_FXEMP_FAILED(-603,"get fx employee failed","获取纷享职员信息失败"),

	CREATE_FXEMP_FAILED(-604,"create fx employee failed","创建纷享职员失败"),

	MODIFY_FXEMP_FAILED(-605,"modify fx employee failed","修改纷享职员失败"),

	STOP_FXEMP_FAILED(-606,"stop fx employee failed","停用纷享职员失败"),
	QUERY_DEPT_DETAIL(-607,"query ding dept failed","查询钉钉部门失败"),

	NOT_SUPPORT_OBJECT(-608, "not support object sync", "不支持的对象同步"),

	GET_ORG_BY_OWNER_FAILED(-701,"get org by owner failed","根据负责人查询其一级部门失败"),
	DING_CALL_BACK_URL_EXIST(-705, "regist call back failed", "企业回调地址已经存在"),
	AVAILABLE_EMPLOYEE_NOT_ENOUGH(-705, "regist call back failed", "企业员工已超额"),
	DEPT_NAME_IS_EXIST(*********, "circle name is exist", "部门名称已存在"),
	EMPLOYEE_NAME_IS_EXIST(*********, "employee name is exist", "人员名称已存在"),

	EMPLOYEE_MOBILE_EXIST(46, "employee mobile is exist", "人员手机号已存在"),
	EMPLOYEE_ACCOUNT_EXIST(********, "employee account is exist", "人员账号已存在"),
	EMPLOYEE_PHONE_BIND_EXIST(********, "employee phone account is exist", "人员账号已存在"),
	EMPLOYEE_IS_EXIST(********, "employee mobile is exist", "CRM人员与钉钉人员信息不一致"),
	ALL_PULL_ORGANIZATION_ING(********, "all pull Organization is initing", "正在全量同步数据，请稍后"),
	EMPLOYEE_IS_STOP(********, "EMPLOYEE HAVEN STOPED", "员工已停用"),
	ENTERPRISE_COUNT_FULL(44, "ENTPRISE_EMP_COUNT_FULL", "员工配额已满"),

	/**
	 * 钉钉云专用错误码
	 */
	CREATE_ORDER_FAILED(9000,"crate crm order fail","创建CRM订单失败"),

	ENTER_PRISE_NOT_AUTH(9001, "ENTERPRISE_NOT_AUTH", "企业没有授权"),

	ENTER_PRISE_INIT(9002, "ENTER_PRISE_INIT", "企业初始化中"),
	BY_CODE_FAIL(9003, "BY_CODE_FAIL", "获取钉钉用户身份失败"),

	DING_EMP_NOT_AUTH(9004, "DING_EMP_NOT_AUTH", "没有权限登录"),

	GET_JSAPI_TICKET_FAILE(9005, "GET_JSAPI_TICKET_FAILE", "获取jsApi失败"),

	GET_APP_AUTH_TICKET_FAILE(9006, "GET_APP_AUTH_TICKET_FAILE", "获取APP auth失败"),

	NOT_QUERY_EMP_INFO(9007, "NOT_QUERY_EMP_INFO", "该员工未绑定"),

	EMP_OR_ENTERPRISE_STOP(9008, "EMP_OR_ENTERPRISE_STOP", "企业停用或员工停用"),

	ENTERPRISE_SERVICE_STOP(9009, "ENTERPRISE_SERVICE_STOP", "企业已停用"),

	USER_NO_LOGIN(9009, "USER_NO_LOGIN", "用户未登录"),

	ENTERPRISE_NOT_SUPPORT_APP(9010, "ENTERPRISE_NOT_SUPPORT_APP", "该企业未授权此应用"),

	GET_PERSON_AUTH_FAIL(9011, "GET_PERSON_AUTH_FAIL", "个人授权获取失败"),

	REFUSE_ENTRANCE(9012, "REFUSE_ENTRANCE", "由于当前应用需要专业版以上版本，您目前使用的CRM标准版暂不支持使用，可联系客服为您处理"),

	NOT_ORDER_DATA(9013, "NOT_ORDER_DATA", "没有订单数据"),
	EMP_IDS_NOT_EMPTY(9014, "EMP_IDS_NOT_EMPTY", "员工列表不能为空"),

   //钉钉云数据源主属返回码
	DING_STORAGE_SUCCESS(200,"DING_STORAGE_SUCCESS","成功"),

	CORP_NOT_AUTH_APP(9015,"CORP_NOT_AUTH_APP","应用未授权"),

	GET_USER_FAIL(9016,"GET_USER_FAIL","获取用户身份信息失败，请退出重进"),
	CREATE_CUSTOMER_FAIL(9017,"CREATE_CUSTOMER_FAIL","创建客户失败"),
	SYNC_ERROR(9018,"SYNC_ERROR","同步过程异常"),
	ADD_USER_ROLE_ERROR(9019, "ADD_USER_ROLE_ERROR", "赋予人员角色失败"),

	CRM_USER_UPPER_LIMIT_INITED(9020, "CRM_USER_UPPER_LIMIT_INITED", "员工配额已满"),

	NO_USER_AUTHORITY(9021,"NO_USER_AUTHORITY","员工不在授权范围内"),
	ENTERPRISE_NAME_NOT_SUPPORT_ALL_NUMBER(9022,"ENTERPRISE_NAME_NOT_SUPPORT_ALL_NUMBER","企业名称不支持全数字"),
	;

	/** 错误码 */
	private int errorCode;

	/** 错误信息 */
	private String errorMessage;

	/** 错误描叙 */
	private String description;

	ResultCode(int errorCode, String errorMessage, String description) {
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
		this.description = description;
	}

	public Integer getErrorCode() {
		return errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public String getDescription() {
		return description;
	}

	public static ResultCode getEnumsByErrorCode(int errorCode) {
		ResultCode[] resultCodesEnums = ResultCode.values();
		for (int i = 0 ; i < resultCodesEnums.length; i++) {
			if (resultCodesEnums[i].errorCode == errorCode) {
				return resultCodesEnums[i];
			}
		}
		return null;
	}
}
