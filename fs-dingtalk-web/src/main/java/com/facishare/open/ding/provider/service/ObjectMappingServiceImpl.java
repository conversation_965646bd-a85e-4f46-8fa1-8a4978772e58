package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.enums.*;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.RedisDingService;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.model.*;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.entity.DingSyncApi;
import com.facishare.open.ding.provider.entity.LogWriteVo;
import com.facishare.open.ding.provider.manager.*;
import com.facishare.open.ding.provider.mongodb.entity.DingSyncLogDO;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.ding.provider.task.executor.DeleteEmployeeTask;
import com.facishare.open.ding.provider.task.executor.InsertEmployeeTask;
import com.facishare.open.ding.provider.task.executor.UpdateEmployeeTask;
import com.facishare.open.ding.provider.task.executor.againMappingTask;
import com.facishare.open.ding.provider.utils.DynamicParamUtil;
import com.facishare.open.ding.provider.utils.RedisLockUtils;
import com.facishare.open.ding.provider.utils.ReorderDeptUtils;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.RemoveEmployeeEventType;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.organization.adapter.api.model.EnterpriseConfigKey;
import com.facishare.organization.adapter.api.model.biz.employee.MobileStatus;
import com.facishare.organization.adapter.api.model.biz.employee.ModifyEmployee;
import com.facishare.organization.adapter.api.model.biz.employee.arg.CreateEmployeeArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.CreateEmployeeResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg;
import com.facishare.organization.api.model.employee.arg.GetNoDepartmentEmployeesDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult;
import com.facishare.organization.api.model.employee.result.GetNoDepartmentEmployeesDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018/7/12 10:58
 */
@Slf4j
@Service("objectMappingService")
// IgnoreI18nFile
public class ObjectMappingServiceImpl implements ObjectMappingService {
    private static final Integer ALLBINDSTATUS = 0;

    private static final Integer CURRENT_EMPLOYEE_ID = -9;

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;

    @Autowired
    private EmployeeProviderService employeeProviderService;

    @Autowired
    private EmployeeService employeeAdapterService;

    @Autowired
    private EnterpriseConfigService enterpriseConfigService;

    @Autowired
    private DingSyncLogManager dingSyncLogManager;

    @Autowired
    private DingDeptService dingDeptService;

    @Autowired
    private RedisDingService redisDingService;

    @Autowired
    private CrmRestManager crmRestManager;

    @Autowired
    private LogManager logManager;

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private DingTodoManager dingTodoManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private ObjectDataManager objectDataManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;

    private static final Integer OPERATOR = -10000;

    private static final String EMP_OBJ = "EmpObj";
    private static final String DEP_OBJ = "DepObj";

    private static final String Symbol = "__";

    private static ExecutorService executorService = new ThreadPoolExecutor(10, 10, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue(10));

//    @Override
//    public Result<Map<String, Object>> queryMappingEmployee(BindEmpVo vo, Integer ei, String appId) {
//        Map<String, Object> dataMap = new HashMap<>();
//        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
//        Integer deptCount = dingDeptService.countByEi(fsEa, appId);
//        dataMap.put("totalCount", dingMappingEmployeeManager.getEmpTotalCount(ei, appId, vo.getBindStatus(), vo.getDingEmployeeName(), vo.getDingEmployeePhone()));
//        dataMap.put("dataList", dingMappingEmployeeManager.queryEmpsBindByEi(ei, appId, vo.getBindStatus(), vo.getPageNumber(), vo.getPageSize(), vo.getDingEmployeeName(), vo.getDingEmployeePhone(), null).getData());
//        return Result.newSuccess(dataMap);
//    }

    @Override
    public Result<List<OuterOaEmployeeBindEntity>> queryInfoByUserId(String unionId, String appId) {
        return dingMappingEmployeeManager.queryEmpByDingUserId(appId, unionId);
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchQueryMapping(Integer ei, List<Integer> crmIds, String appId) {
        Result<List<DingMappingEmployeeResult>> mappingResult = dingMappingEmployeeManager.batchQueryMapping(ei, appId, crmIds);
        return mappingResult;
    }

    @Override
    public Result<List<Integer>> queryMappingCrm(Integer ei, String appId) {
        Result<List<Integer>> listResult = dingMappingEmployeeManager.queryEmpList(ei, appId);
        return listResult;
    }
//
//    @Override
//    public Result<Map<String, Object>> queryNewEmployee(BindEmpVo vo, Integer ei, String ea, Integer userId, String appId) {
//        //两种模式 第一种模式是没有员工时去拉取员工，第二种模式是部门表为空的时候 去拉取员工
//        Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
//        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
//        if (dingEnterpriseResultResult.getData() != null) {
//            if (dingEnterpriseResultResult.getData().getDevModel().equals(1)) {
//                Integer allCount = dingMappingEmployeeManager.getEmpAllCount(ei, appId);
//                if (Objects.nonNull(allCount) && allCount > 0) {
//                    Result<Map<String, Object>> result = queryMappingEmployee(vo, ei, appId);
//                    return result;
//                }
//                Thread thread = new Thread() {
//                    public void run() {
//                        initDingEmployee(ei, userId, appId);
//                    }
//                };
//                thread.start();
//                return Result.newError(ResultCode.INIT_DING_EMP);
//            } else {
//                Integer deptCount = dingDeptService.countByEi(fsEa, appId);
//                if (Objects.nonNull(deptCount) && deptCount > 0) {
//                    Result<Map<String, Object>> result = queryMappingEmployee(vo, ei, appId);
//                    return result;
//                }
//                executorService.execute(new Runnable() {
//                    @Override
//                    public void run() {
//                        initModelEmployee(ei, userId, ea, appId);
//                    }
//                });
//                return Result.newError(ResultCode.INIT_DING_EMP);
//            }
//        }
//        return Result.newError(ResultCode.INIT_DING_EMP);
//
//    }

    @Override
    public Result<Map<String, Object>> syncPullOrganizationData(Integer ei, String ea, Integer userId, String appId) {
        initModelEmployee(ei, userId, ea, appId);
        return Result.newError(ResultCode.ALL_PULL_ORGANIZATION_ING);
    }


    @Override
    public Result<Boolean> syncNewEmployee(Integer ei, Integer userId, String appId) {
        //判断该企业的回调地址是否在使用
        Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if (dingEnterpriseResultResult.getData().getIsCallback() == Constant.IS_NOT_CALLBACK) {
            Thread thread = new Thread() {
                public void run() {
                    allPullEmployee(ei, userId, appId);
                }
            };
            thread.start();
            return Result.newSuccess(Boolean.TRUE);
        }
        return Result.newSuccess(Boolean.FALSE);
    }

    //模式二拉取员工
    @Override
    public Result<Void> initModelEmployee(Integer ei, Integer userId, String ea, String appId) {
        StopWatch stopWatch = StopWatch.create("initModelEmployee");

        log.info("pullEmployee dingding emp start ei={}.", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        //查询部门列表
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DEPT_LIST_ERROR);
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {

            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<Dept> depts = new ArrayList<>();
            List<Dept> detailList = Lists.newArrayList();
            if (scopeVo.getAuthedDept().get(0) == 1) {
                detailList = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, mappingEnterprise.getData().getToken(), "1");
                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn("query dept list failed, ei={}, deptResponse={}.", ei, detailList);
                    return Result.newError(ResultCode.DEPT_LIST_ERROR);
                }
            } else {
                for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                    List<Long> deptIdList = new ArrayList<>();
                    //先把父级部门查询出来
                    List<Dept> deptItem = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                            appKey, appSecret, mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i).toString());
                    List<Long> collect = deptItem.stream().map(Dept::getId)
                            .collect(Collectors.toList());
                    deptIdList.add(scopeVo.getAuthedDept().get(i));
                    deptIdList.addAll(collect);
                    depts.addAll(deptItem);
                    for (int j = 0; j < deptIdList.size(); j++) {
                        Dept deptDetail = DingRequestUtil.queryDeptDetail(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), deptIdList.get(j));
                        detailList.add(deptDetail);
                    }
                }
                List<Long> scopeCollect = detailList.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(scopeCollect);
            }
            //创建部门
            List<Dept> finalDetailList = new ArrayList<>();
            finalDetailList.addAll(detailList);
            //同步组织架构
            recallInitDept(detailList, ei, ea, appId);
            //查询部门下所有员工，另起线程同步员工数量
            log.info("finalDetail size:{}", finalDetailList.size());
            for (int i = 0; i < finalDetailList.size(); i++) {
                //查询部门信息
                Dept dept = finalDetailList.get(i);
                insertMappingEmp(dept, ei, mappingEnterprise.getData().getClientIp(), appId);
            }
            //添加部门负责人
            log.info("createDeptOwner starting");
            createDeptOwner(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), ei, appId);
        }

        //如果选择是部分员工，需要同步员工
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedUser())) {
            List<User> differEmployeeList = Lists.newArrayList();
            scopeVo.getAuthedUser().parallelStream().filter(item -> !isExistData(ei, item, appId)).forEach(item -> {
                //添加未加入的员工
                User user = DingRequestUtil.getUser(mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, item, mappingEnterprise.getData().getToken());
                differEmployeeList.add(user);
            });
            Integer count = dingMappingEmployeeManager.initMappingEmployee(differEmployeeList, ei, null, StringUtils.EMPTY, appId);
            if (Objects.nonNull(count) && count.equals(differEmployeeList.size())) {
                log.info("初始化员工成功" + count + "条");
            } else {
                log.warn("初始化员工失败,员工可能已经存在,deptId={},deptName={}.", differEmployeeList.get(0).getUserid(), differEmployeeList.get(0).getName());
            }
        }

        stopWatch.lap("createEmployee");
        stopWatch.log();

        return Result.newSuccess();
    }

    private void insertMappingEmp(Dept dept, Integer ei, String clientIp, String appId) {
        //查询部门信息
        Long deptId = dept.getId();
        String deptName = dept.getName();
        log.info("trace allPullEmployee size,deptIndex:{},deptId:{}", dept, dept.getId());
        UserDetailVo userDetailVo = DingRequestUtil.queryUserByDeptId(clientIp, tokenManager.getToken(ei, appId), deptId.intValue());

        if (ObjectUtils.isEmpty(userDetailVo) || CollectionUtils.isEmpty(userDetailVo.getUserlist())) {
            log.info("this dept no user, ei={},deptId={}.", ei, dept);
            return;
        }
        List<User> deptUsers = Lists.newArrayList();
        userDetailVo.getUserlist().stream().forEach(item -> {
            User user = new User();
            BeanUtils.copyProperties(item, user);
            log.info("item :{},user:{}", item, user);
            deptUsers.add(user);
        });

        //保存钉钉员工信息
        log.info("this dept  user={}, ei={},deptId={}.,userSize:{}", deptUsers, ei, dept, deptUsers.size());
        Integer count = dingMappingEmployeeManager.initMappingEmployee(deptUsers, ei, deptId, deptName, appId);
        if (Objects.nonNull(count) && count.equals(deptUsers.size())) {
            log.info("初始化员工成功" + count + "条");
        } else {
            log.warn("初始化员工失败,员工可能已经存在,deptId={},deptName={}.", deptId, deptName);
        }

    }


    //解析部门信息，返回出主管列表设置部门负责人

    @Override
    public Result<Void> allPullEmployeeInsert(Integer ei, Integer userId, String ea, String appId) {
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        List<Dept> detailList = requestDingAllDept(ei, userId, ea, appId);
        //插入员工
        log.info("trace allPullEmployee size:{}", detailList.size());

        for (int i = 0; i < detailList.size(); i++) {
            //查询部门信息
            Dept dept = detailList.get(i);
            Long deptId = dept.getId();
            String deptName = dept.getName();
            insertMappingEmp(dept, ei, mappingEnterprise.getData().getClientIp(), appId);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> upsertAllDept(Integer ei, Integer userId, String ea, String appId) {
        //读取部门信息
        List<Dept> allDingDept = requestDingAllDept(ei, userId, ea, appId);
        //转换成map结构
        Map<Long, Dept> DingDeptMap = allDingDept.stream().collect(Collectors.toMap(Dept::getId, Function.identity(), (key1, key2) -> key2));
        List<DeptVo> deptTable = dingDeptService.getDeptByEI(ea, appId);
        Map<Long, DeptVo> tableMap = deptTable.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));

        allDingDept.stream().forEach(item -> {
            //如果是中间表没有的映射关系，则新建
            if (ObjectUtils.isEmpty(tableMap.get(item.getId()))) {
                supportAddDept(ei, ea, item, DingDeptMap, tableMap, appId);
            } else {
                //如果是中间表存在的映射关系，则更新
                executorService.execute(new Runnable() {
                    @Override
                    public void run() {
                        supportUpdateDept(ei, ea, item, DingDeptMap, tableMap, appId);
                    }
                });
//               supportUpdateDept(ei,ea,item,DingDeptMap,tableMap);
            }
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> fixNoMainDept(Integer ei, Integer userId, String ea, String appId) {
        //查询没有主部门的员工
        GetNoDepartmentEmployeesDtoArg arg = new GetNoDepartmentEmployeesDtoArg();
        arg.setEnterpriseId(ei);
        arg.setMainDepartment(MainDepartment.MAIN);
        arg.setRunStatus(RunStatus.ALL);
        GetNoDepartmentEmployeesDtoResult noDepartmentEmployees = employeeProviderService.getNoDepartmentEmployees(arg);
        if (ObjectUtils.isNotEmpty(noDepartmentEmployees)) {
            List<EmployeeDto> employeeDtos = noDepartmentEmployees.getEmployeeDtos();
            //根据员工的ID去查询在钉钉的员工ID
            List<Integer> emps = employeeDtos.stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
            Result<List<DingMappingEmployeeResult>> dingResult = dingMappingEmployeeManager.fixNoMainDeptByUserID(ei, emps, appId);
            List<DingMappingEmployeeResult> dingEmpData = dingResult.getData();
            Map<String, DingMappingEmployeeResult> employeeDtoMap = dingEmpData.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingEmployeeId, Function.identity(), (key1, key2) -> key2));
            List<DeptVo> deptList = dingDeptService.getDeptByEI(ea, appId);
            Map<Long, DeptVo> deptMaps = deptList.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));

            String accessToken = tokenManager.getToken(ei, appId);
            Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
            String clientIp = enterpriseResult.getData().getClientIp();
            for (int i = 0; i < dingEmpData.size(); i++) {
                //一次请求20个员工
                int end = i + 20 > dingEmpData.size() ? dingEmpData.size() : i + 20;
                List<DingMappingEmployeeResult> items = dingEmpData.subList(i, end);
                i += 19;
//                executorService.execute(new Runnable() {
//                    @Override
//                    public void run(){
                StringBuilder employees = new StringBuilder();
                items.stream().forEach(item -> {
                    employees.append(item.getDingEmployeeId()).append(",");
                });
                log.info("again mapping employee list:{},end：{}", employees, end);
                accessToken = tokenManager.getToken(ei, appId);
                List<EmployeeDingVo> smartWorkList = DingRequestUtil.getSmartWorkList(employees.toString(), accessToken, clientIp);
                //全量更新
                smartWorkList.stream().forEach(item -> {
                    log.info("again mapping item:{}", item);
                    DingMappingEmployeeResult mappingResult = employeeDtoMap.get(item.getUserid());
                    ModifyEmployee.Argument argument = new ModifyEmployee.Argument();
                    argument.setEnterpriseId(ei);
                    argument.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
                    argument.setEmployeeId(mappingResult.getEmployeeId());
                    argument.setMobile(item.getMobile());
                    DeptVo deptVo = deptMaps.get(item.getMainDept());
                    argument.setMainDepartmentId(deptVo.getCrmDeptId());
                    //附属部门
                    List<Integer> depts = treeDeptIds((HashMap<Long, DeptVo>) deptMaps, item.getMainDept(), new ArrayList<Integer>());
                    argument.setViceDepartmentIds(depts);
                    log.info("againMappingEmployee starting ....:{}", argument);
                    ModifyEmployee.Result modifyResult = null;
                    try {
                        modifyResult = employeeAdapterService.modifyEmployeeV2(argument);
                        log.info("modify argument ei:{},arg:{},result:{}", ei, argument, modifyResult);
                    } catch (OrganizationException e) {
                        log.info("modify argument ei:{},arg:{},result:{}", ei, argument, modifyResult);
                        e.printStackTrace();
                    }
                });
//                    }
//                });
            }
        }

        return null;
    }

    @Override
    public Result<Integer> deleteEmpByDingId(Integer ei, String dingEmp, String appId) {
        Integer count = dingMappingEmployeeManager.relieveBind(ei, dingEmp, appId);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> deleteEmpByFsId(Integer ei, List<Integer> fsEmpIds) {
        Integer count = dingMappingEmployeeManager.deleteBindByFsId(ei, fsEmpIds);
        return Result.newSuccess(count);
    }

    @Override
    public Result<DingEnterpriseResult> queryEnterpriseByEi(Integer ei, String appId) {
        return dingEnterpriseManager.queryEnterpriseByEi(ei);
    }

    @Override
    public Result<Integer> getSourceCount(int ei, String sourceId, String appId) {
        Integer sourceCount = dingTodoManager.getSourceCount(ei, sourceId, appId);
        return Result.newSuccess(sourceCount);
    }

    @Override
    public Result<String> queryUserId(int ei, int receiverId, String appId) {
        String userId = dingTodoManager.queryUserId(ei, receiverId, appId);
        return Result.newSuccess(userId);
    }

    @Override
    public Result<List<String>> queryUserIds(int ei, List<Integer> receiverIds, String appId) {
        return Result.newSuccess(dingTodoManager.queryUserIds(ei, receiverIds, appId));
    }

    @Override
    public Result<List<DingTaskVo>> getDingTaskVo(int ei, String sourceId, String appId) {
        return Result.newSuccess(dingTodoManager.getDingTaskVo(ei, sourceId, appId));
    }

    @Override
    public Result<Integer> insertSource(DingTaskVo dingTaskVo, String appId) {
        return Result.newSuccess(dingTodoManager.insertSource(dingTaskVo, appId));
    }

    @Override
    public Result<Integer> updateStatus(int status, int ei, String sourceId, int employeeId, String appId) {
        return Result.newSuccess(dingTodoManager.updateStatus(status, ei, sourceId, employeeId, appId));
    }

    @Override
    public Result<Integer> updateExecutor(List<Integer> deleteEmployeeIds, int ei, String sourceId, String appId) {
        return Result.newSuccess(dingTodoManager.updateExecutor(deleteEmployeeIds, ei, sourceId, appId));
    }

    @Override
    public Result<UserVo> getUserDetail(String accessToken, String userId, String clientIp, String appId) {
        return Result.newSuccess(DingRequestUtil.getUserDetail(accessToken, userId, clientIp));
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchGetEmpIds(Integer ei, List<Integer> empIds, String appId) {
        Result<List<DingMappingEmployeeResult>> listResult = dingMappingEmployeeManager.batchGetEmpIds(ei, empIds, appId);
        return listResult;
    }

    private void supportAddDept(Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, Map<Long, DeptVo> tableMap, String appId) {
        //直接调用接口请求钉钉部门的所有的上级
        log.info("重新映射新增钉钉部门 ea:{},dept:{}", ea, dept);
        DingEnterpriseResult enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei).getData();
        List<Long> listParent = DingRequestUtil.getListParent(enterprise.getClientIp(), dept.getId(), tokenManager.getToken(ei, appId));
        Collections.reverse(listParent);
        //找出没有创建的上级部门并创建
        LinkedList<Dept> depts = Lists.newLinkedList();
        listParent.stream().forEach(item -> {
            DeptVo temp = tableMap.get(item.longValue());
            if (ObjectUtils.isNotEmpty(temp)) {
                return;
            }
            // 检查是否有其他appId已经映射了
            final String outDeptId = String.valueOf(item);
            final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, outDeptId);
            if (StringUtils.isNotEmpty(fsDeptId)) {
                // 写入绑定表
                tableMap.put(item.longValue(), dingDeptService.queryByDingId(ea, appId, outDeptId));
                return;
            }
            depts.add(DingDeptMap.get(item.longValue()));
        });
        log.info("重新映射新增钉钉部门 ea:{},dept:{}", ea, depts);
        //MetaParam metaParam = new MetaParam(enterprise.getEa(), enterprise.getCreateBy());
        commonCreateUpDept(ei, depts, tableMap, appId);
    }

    private Map<Long, DeptVo> commonCreateUpDept(Integer ei, LinkedList<Dept> depts, Map<Long, DeptVo> tableMap, String appId) {
        depts.stream().forEach(item -> {
            //查询父级部门
            DeptVo deptVo = tableMap.get(item.getParentid());
            Integer parentId = Optional.ofNullable(deptVo.getCrmDeptId()).orElse(Constant.TREE_PARENT_ID);
            //BeanResult<CreatedCircle> circle = circleService.createCircle(metaParam, validName(item.getName()), parentId);
            DeptVo vo = new DeptVo();
            vo.setEi(ei);
            vo.setName(validName(item.getName()));
            vo.setCrmParentId(parentId);
            Result<Integer> deptResult = crmRestManager.createDept(vo);
            log.info("circleBeanResult objectMapping :{},deptName:{}", deptResult, item.getName());
            DingSyncApi syncApi = new DingSyncApi();
            syncApi.setApiName(DEP_OBJ);
            syncApi.setSyncDirection(2);
            DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, item.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
            String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + item.getName() + "]";
            String content = String.format("[%s] 创建纷享部门，数据流向为[%s]", item.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
            DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
            dingSyncLogManager.logDingSync(kcSyncLogDO);
            Map<String, Object> sameMap = Maps.newHashMap();
            if (deptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                sameMap = fixCallBackName(ei, item, appId);
                if (Boolean.valueOf(sameMap.get("same").toString())) {
                    //如果是true，上级不同名
                    //创建部门
                    //circle = circleService.createCircle(metaParam, sameMap.get("name").toString(), parentId);
                    vo.setName(sameMap.get("name").toString());
                    deptResult = crmRestManager.createDept(vo);
                }
            }
            //查询crm的父级部门ID
//            BeanResult<Circle> circleNoAdminId = circleService.getCircleNoAdminId(enterprise.getEa(), circle.getResult().getCircleId());
//            DeptVo vo = new DeptVo();
            vo.setEi(ei);
            vo.setDingDeptId(item.getId());
            vo.setDingParentId(item.getParentid());
            vo.setName(item.getName());
            vo.setDingDeptOwner(item.getDeptOwner());
            vo.setCrmDeptId(deptResult.getData());
            vo.setCrmParentId(parentId);
            //vo.setSeq(circle.getResult().getOrder());
            vo.setCreateTime(new Date());
            vo.setUpdateTime(new Date());
            tableMap.put(item.getId(), vo);
            Integer count = dingDeptService.addDeptByEi(vo, eieaConverter.enterpriseIdToAccount(ei), appId);
            if (count != 0) {
                log.info("insert ding_dept row:{},ei:{},deptVo:{}", count, ei, vo);
            }
        });
        return tableMap;
    }


    private Map<Long, DeptVo> supportUpdateDept(Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, Map<Long, DeptVo> tableMap, String appId) {
        //更新部门信息
        log.info("重新映射更新钉钉部门 ea:{},dept:{}", ea, dept);
        DingEnterpriseResult enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei).getData();
        List<Long> listParent = DingRequestUtil.getListParent(enterprise.getClientIp(), dept.getId(), tokenManager.getToken(ei, appId));
        Collections.reverse(listParent);
        //如果上级部门不存在，则需要先新增父部门
        LinkedList<Dept> depts = Lists.newLinkedList();
        for (int i = 0; i < listParent.size(); i++) {
            DeptVo temp = tableMap.get(listParent.get(i).longValue());
            if (ObjectUtils.isEmpty(temp)) {
                final String outDeptId = String.valueOf(temp.getDingDeptId());
                final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, outDeptId);
                if (StringUtils.isNotEmpty(fsDeptId)) {
                    // 写入绑定表
                    tableMap.put(temp.getDingDeptId(), dingDeptService.queryByDingId(ea, appId, outDeptId));
                } else {
                    depts.add(DingDeptMap.get(listParent.get(i).longValue()));
                }
            }
        }
        log.info("更新部门ing,重新映射新增钉钉部门 ea:{},dept:{}", ea, depts);
        //MetaParam metaParam = new MetaParam(enterprise.getEa(), Constant.SYSTEM_USER);
        tableMap = commonCreateUpDept(ei, depts, tableMap, appId);
        Integer crmPart = tableMap.get(listParent.get(listParent.size() - 1).longValue()).getCrmParentId();
        String name = validName(dept.getName());
        DeptVo updateDeptVo = tableMap.get(dept.getId());
        Integer crmID = updateDeptVo.getCrmDeptId();
        //BaseResult baseResult = circleService.updateCircle(metaParam, crmID, name, crmPart, null);
        Result<Void> baseResult = crmRestManager.modifyDept(ei, crmID, name, crmPart, null);
        Map<String, Object> sameMap = Maps.newHashMap();
        if (baseResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
            //如果部门名称已经存在，获取上级部门名称作为前缀
            sameMap = fixCallBackName(ei, dept, appId);
            if (Boolean.valueOf(sameMap.get("same").toString())) {
                //如果是true，上级不同名
                //创建部门
                //baseResult = circleService.updateCircle(metaParam, crmID, validName(sameMap.get("name").toString()), crmPart, null);
                name = validName(sameMap.get("name").toString());
                baseResult = crmRestManager.modifyDept(ei, crmID, name, crmPart, null);
                dept.setName(sameMap.get("name").toString());
            }
        }
        DingSyncApi syncApi = new DingSyncApi();
        syncApi.setApiName(DEP_OBJ);
        syncApi.setSyncDirection(2);
        DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, dept.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.UPDATE.getType());
        String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + dept.getName() + "]";
        String content = String.format("[%s] 修改纷享部门，数据流向为[%s]", dept.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
        DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
        dingSyncLogManager.logDingSync(kcSyncLogDO);
        log.info("callback controller modifyDepartmentResult result:{}", baseResult);
        //更新数据库
        //更新名字/上级部门/主管
        updateDeptVo.setName(dept.getName());
        updateDeptVo.setDingParentId(dept.getParentid());
        updateDeptVo.setDingDeptOwner(dept.getDeptOwner());
        updateDeptVo.setCrmParentId(crmPart);
        updateDeptVo.setCrmDeptId(crmID);
        Result<Integer> integerResult = dingDeptService.updateDept(updateDeptVo, eieaConverter.enterpriseIdToAccount(ei), appId);
        log.info("supportUpdateDept insert:{}", integerResult.getData());
        return tableMap;
    }


    private List<Dept> requestDingAllDept(Integer ei, Integer userId, String ea, String appId) {
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        List<Dept> detailList = Lists.newArrayList();
        //查询部门列表
        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return detailList;
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return detailList;
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {

            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<Dept> depts = new ArrayList<>();
            if (scopeVo.getAuthedDept().get(0) == 1) {
                detailList = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, mappingEnterprise.getData().getToken(), "1");
                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn("query dept list failed, ei={}, deptResponse={}.", ei, detailList);
                    return detailList;
                }
                Dept deptDetail = DingRequestUtil.queryDeptDetail(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), 1L);
                detailList.add(deptDetail);
            } else {
                for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                    List<Long> deptIdList = new ArrayList<>();
                    //先把父级部门查询出来
                    List<Dept> deptItem = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                            appKey, appSecret, mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i).toString());
                    List<Long> collect = deptItem.stream().map(Dept::getId)
                            .collect(Collectors.toList());
                    deptIdList.add(scopeVo.getAuthedDept().get(i));
                    deptIdList.addAll(collect);
                    depts.addAll(deptItem);
                    for (int j = 0; j < deptIdList.size(); j++) {
                        Dept deptDetail = DingRequestUtil.queryDeptDetail(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), deptIdList.get(j));
                        detailList.add(deptDetail);
                    }
                }
                List<Long> scopeCollect = detailList.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(scopeCollect);
            }
        }
        return detailList;
    }


    //递归创建部门
    public void recallInitDept(List<Dept> depts, Integer ei, String ea, String appId) {
        log.info("recallInitDept ：{}", ei);
        List<Dept> orderDepts = ReorderDeptUtils.reorderDingdingDepartments(depts);
        //更新或者创建部门
        obtainOrderDept(ei, ea, orderDepts, appId);
    }

    //创建部门
//    public void initDepartment(DingEnterpriseResult result, List<Dept> depts, Integer ei, String ea, Integer userId) {
//        //把部门放进map集合中，在创建部门的时候请求钉钉接口获取该所有的上级部门
//        Map<Long, Dept> DingDeptMap = depts.stream().collect(Collectors.toMap(Dept::getId, Function.identity(), (key1, key2) -> key2));
//
//        MetaParam metaParam = new MetaParam(ea, userId);
//        ConcurrentHashMap<Long, CircleVo> deptMap = new ConcurrentHashMap();
//        //兼容已经存在的部门，需要先获取已经创建的部门
//        ListResult<Circle> allCircles = circleService.getAllCircles(metaParam);
//
//        for (int i = 0; i < depts.size(); i++) {
//            List<Long> listParent = DingRequestUtil.getListParent(result.getClientIp(), depts.get(i).getId(), tokenManager.getToken(ei, appId));
//            int dingPar = 1;
//            Collections.reverse(listParent);
//            BeanResult<CreatedCircle> circleBeanResult = null;
//            //创建部门：
//            for (int j = 0; j < listParent.size(); j++) {
//                //deptMap存在上级部门应该跳过循环
//                if (ObjectUtils.isEmpty(DingDeptMap.get(listParent.get(j).longValue())) || ObjectUtils.isNotEmpty(deptMap.get(listParent.get(j).longValue()))) {
//                    continue;
//                }
//                if (ObjectUtils.isNotEmpty(deptMap.get(listParent.get(j - 1 < 0 ? 0 : j - 1).longValue()))) {
//                    CircleVo circleVo = deptMap.get(listParent.get(j - 1 < 0 ? 0 : j - 1).longValue());
//                    log.info("createCrmDesk circleVo:{}", circleVo);
//                    dingPar = Optional.ofNullable(circleVo.getCircleId()).orElse(1);
//
//                }
//                String deptName = validName(DingDeptMap.get(listParent.get(j).longValue()).getName());
//                circleBeanResult = circleService.createCircle(metaParam, deptName, dingPar == 1 ? Constant.TREE_PARENT_ID : dingPar);
//                log.info("circleBeanResult objectMapping :{},deptName:{}", circleBeanResult, deptName);
//                if (circleBeanResult.getErrCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
//                    List<Circle> circleVos = allCircles.getResult().stream().filter(item -> item.getName().equals(deptName)).collect(Collectors.toList());
//                    if (org.apache.commons.collections.CollectionUtils.isEmpty(circleVos)) {
//                        //避免刚才创建的部门重名，重新获取一次allCircles
//                        try {
//                            Thread.sleep(2000);
//                        } catch (InterruptedException e) {
//                            e.printStackTrace();
//                        }
//                        allCircles = circleService.getAllCircles(metaParam);
//                        circleVos = allCircles.getResult().stream().filter(item -> item.getName().equals(deptName)).collect(Collectors.toList());
//                        if (CollectionUtils.isEmpty(circleVos)) break;
//                    }
//                    //处理同名的时候，会出现两种去情况：一是在钉钉侧确实存在同名，二是已经在crm存在该部门了
//                    Map<String, Object> sameMap = fixSameDeptName(DingDeptMap, depts.get(i), circleVos.get(0), ea);
//                    if (Boolean.valueOf(sameMap.get("same").toString())) {
//                        //如果是true，上级不同名
//                        //创建部门
//                        BeanResult<CreatedCircle> againResult = circleService.createCircle(metaParam, sameMap.get("name").toString(), dingPar == 1 ? Constant.TREE_PARENT_ID : dingPar);
//                        if (againResult.getErrCode() == Constant.SUCCESS_CODE) {
//                            circleVos.get(0).setCircleId(againResult.getResult().getCircleId());
//                            circleVos.get(0).setParentId(dingPar == 1 ? Constant.TREE_PARENT_ID : dingPar);
//                            circleVos.get(0).setName(sameMap.get("name").toString());
//                            circleVos.get(0).setOrder(againResult.getResult().getOrder());
//                        }
//                    }
//                    CircleVo circleVo = new CircleVo();
//                    circleVo.setCircleId(circleVos.get(0).getCircleId());
//                    circleVo.setCircleParentId(circleVos.get(0).getParentId());
//                    circleVo.setOrder(circleVos.get(0).getOrder());
//                    circleVo.setCirceName(circleVos.get(0).getName());
//                    deptMap.put(listParent.get(j).longValue(), circleVo);
//                    //创建日志
//                    DingSyncApi syncApi = new DingSyncApi();
//                    syncApi.setApiName(DEP_OBJ);
//                    syncApi.setSyncDirection(2);
//                    executorService.execute(new Runnable() {
//                        @Override
//                        public void run() {
//                            DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, deptName, OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
//                            String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + deptName + "]";
//                            String content = String.format("[%s] 纷享部门已存在，数据流向为[%s]", deptName, DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
//                            DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
//                            dingSyncLogManager.logDingSync(kcSyncLogDO);
//                        }
//                    });
//                }
//                if (circleBeanResult.getErrCode() == Constant.errcode) {
//                    CircleVo circleVo = new CircleVo();
//                    BeanUtils.copyProperties(circleBeanResult.getResult(), circleVo);
//                    circleVo.setCircleParentId(dingPar);
//                    circleVo.setCirceName(deptName);
//                    deptMap.put(listParent.get(j).longValue(), circleVo);
//                    DingSyncApi syncApi = new DingSyncApi();
//                    syncApi.setApiName(DEP_OBJ);
//                    syncApi.setSyncDirection(2);
//                    executorService.execute(new Runnable() {
//                        @Override
//                        public void run() {
//                            DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, deptName, OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
//                            String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + deptName + "]";
//                            String content = String.format("[%s] 纷享部门已初始化，数据流向为[%s]", deptName, DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
//                            DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
//                            dingSyncLogManager.logDingSync(kcSyncLogDO);
//                        }
//                    });
//
//                }
//            }
//        }
//        //更新部门数据库的数据
//        List<DeptVo> voList = Lists.newArrayList();
//        log.info("insert deptMapping start");
//        depts.stream().forEach(item -> {
//            log.info("objectMapping impl item:{},map:{}", item, deptMap.get(item.getId()));
//            DeptVo deptVo = new DeptVo();
//            deptVo.setEi(ei);
//            deptVo.setDingDeptId(item.getId());
//            deptVo.setDingParentId(item.getParentid());
//            deptVo.setName(item.getName());
//            deptVo.setDingDeptOwner(Optional.ofNullable(item.getDeptOwner()).orElse(StringUtils.EMPTY));
//            //
//            CircleVo circleVo = Optional.ofNullable(deptMap.get(item.getId())).orElse(new CircleVo());
//            deptVo.setCrmDeptId(circleVo.getCircleId());
//            deptVo.setCrmParentId(circleVo.getCircleParentId());
//            deptVo.setSeq(circleVo.getOrder());
//            deptVo.setCreateTime(new Date());
//            deptVo.setUpdateTime(new Date());
//            voList.add(deptVo);
//        });
//        Integer row = dingDeptMananger.initDeptManager(voList, ei, userId);
//        log.info("insert dept row:{},ei:{}", row, ei);
//    }

    //递归创建钉钉部门
//    private ConcurrentHashMap<Long, CircleVo> recallCreateDept(Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, ConcurrentHashMap<Long, CircleVo> deptMap) {
//        if (ObjectUtils.isEmpty(DingDeptMap.get(dept.getId())) || ObjectUtils.isNotEmpty(deptMap.get(dept.getId())))
//            return deptMap;
//        if (ObjectUtils.isNotEmpty(DingDeptMap.get(dept.getParentid())) && ObjectUtils.isEmpty(deptMap.get(dept.getId()))) {
//            recallCreateDept(ei, ea, DingDeptMap.get(dept.getParentid()), DingDeptMap, deptMap);
//        }
//        //创建部门
//        String deptName = validName(dept.getName());
//        MetaParam metaParam = new MetaParam(ea, Constant.SYSTEM_USER);
//
//        Long parentId = Optional.ofNullable(dept.getParentid()).orElse(1L);
//        CircleVo vo = Optional.ofNullable(deptMap.get(parentId)).orElse(new CircleVo());
//        Integer dingPar = vo.getCircleId() != 0 ? vo.getCircleId() : Constant.TREE_PARENT_ID;
//        BeanResult<CreatedCircle> result = circleService.createCircle(metaParam, deptName, dingPar);
//        log.info("circleBeanResult objectMapping :{},deptName:{}", result, deptName);
//
//        if (result.getErrCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
//            ListResult<Circle> allCircles = circleService.getAllCircles(metaParam);
//            List<Circle> circleVos = allCircles.getResult().stream().filter(item -> item.getName().equals(deptName)).collect(Collectors.toList());
//            if (org.apache.commons.collections.CollectionUtils.isEmpty(circleVos)) {
//                //避免刚才创建的部门重名，重新获取一次allCircles
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                allCircles = circleService.getAllCircles(metaParam);
//                circleVos = allCircles.getResult().stream().filter(item -> item.getName().equals(deptName)).collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(circleVos)) return deptMap;
//            }
//            //处理同名的时候，会出现两种去情况：一是在钉钉侧确实存在同名，二是已经在crm存在该部门了
//            Map<String, Object> sameMap = fixSameDeptName(DingDeptMap, dept, circleVos.get(0), ea);
//            if (Boolean.valueOf(sameMap.get("same").toString())) {
//                //如果是true，上级不同名
//                //创建部门
//                BeanResult<CreatedCircle> againResult = circleService.createCircle(metaParam, sameMap.get("name").toString(), dingPar);
//                if (againResult.getErrCode() == Constant.SUCCESS_CODE) {
//                    circleVos.get(0).setCircleId(againResult.getResult().getCircleId());
//                    circleVos.get(0).setParentId(dingPar);
//                    circleVos.get(0).setName(sameMap.get("name").toString());
//                    circleVos.get(0).setOrder(againResult.getResult().getOrder());
//                }
//            }
//            CircleVo circleVo = new CircleVo();
//            circleVo.setCircleId(circleVos.get(0).getCircleId());
//            circleVo.setCircleParentId(circleVos.get(0).getParentId());
//            circleVo.setOrder(circleVos.get(0).getOrder());
//            circleVo.setCirceName(circleVos.get(0).getName());
//            deptMap.put(dept.getId(), circleVo);
//            //创建日志
//            DingSyncApi syncApi = new DingSyncApi();
//            syncApi.setApiName(DEP_OBJ);
//            syncApi.setSyncDirection(2);
//            executorService.execute(new Runnable() {
//                @Override
//                public void run() {
//                    DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, deptName, OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
//                    String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + deptName + "]";
//                    String content = String.format("[%s] 纷享部门已存在，数据流向为[%s]", deptName, DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
//                    DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
//                    dingSyncLogManager.logDingSync(kcSyncLogDO);
//                }
//            });
//        }
//        if (result.getErrCode() == Constant.errcode) {
//            CircleVo circleVo = new CircleVo();
//            BeanUtils.copyProperties(result.getResult(), circleVo);
//            circleVo.setCircleParentId(dingPar);
//            circleVo.setCirceName(deptName);
//            deptMap.put(dept.getId(), circleVo);
//            DingSyncApi syncApi = new DingSyncApi();
//            syncApi.setApiName(DEP_OBJ);
//            syncApi.setSyncDirection(2);
//            executorService.execute(new Runnable() {
//                @Override
//                public void run() {
//                    DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, deptName, OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
//                    String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + deptName + "]";
//                    String content = String.format("[%s] 纷享部门已初始化，数据流向为[%s]", deptName, DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
//                    DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
//                    dingSyncLogManager.logDingSync(kcSyncLogDO);
//                }
//            });
//
//        }
//        return deptMap;
//    }

    //递归创建钉钉部门
    private ConcurrentHashMap<Long, Dept> recallConstructDept
    (Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, ConcurrentHashMap<Long, Dept> deptMap, Map<String, Dept> nameDept, LinkedList<Dept> linkDepts) {
        if (ObjectUtils.isEmpty(DingDeptMap.get(dept.getId())) || ObjectUtils.isNotEmpty(deptMap.get(dept.getId())))
            return deptMap;
        if (ObjectUtils.isNotEmpty(DingDeptMap.get(dept.getParentid())) && ObjectUtils.isEmpty(deptMap.get(dept.getId()))) {
            recallConstructDept(ei, ea, DingDeptMap.get(dept.getParentid()), DingDeptMap, deptMap, nameDept, linkDepts);
        }
        //创建部门
//        if(ObjectUtils.isNotEmpty(nameDept.get(dept.getName()))){
//            String parentId=deptMap.get
//        }
        deptMap.put(dept.getId(), dept);
        linkDepts.addLast(dept);
        return deptMap;
    }

    @Override
    public Result<Void> obtainOrderDept(Integer ei, String ea, List<Dept> orderDepts, String appId) {
        if (ObjectUtils.isEmpty(orderDepts) || CollectionUtils.isEmpty(orderDepts)) {
            log.info("obtainOrder dept is null ei:{}", ei);
        }

        log.info("obtainOrderDept:{}", ei);
        //创建crm部门返回的result
        DeptVo parentDeptVo = null;
        Integer parentCrmId = Constant.TREE_PARENT_ID;
        Integer CrmId = null;
        Integer order = null;
        for (int i = 0; i < orderDepts.size(); i++) {
            Dept originDept = orderDepts.get(i);
            originDept.setName(validName(originDept.getName()));
            //部门在crm的数据
            DeptVo crmData = queryDeptData(ei, appId, originDept.getId());
            //父级部门在crm的数据
            DeptVo crmParentData = queryDeptData(ei, appId, originDept.getParentid());
            if (ObjectUtils.isNotEmpty(crmData)) {
                parentCrmId = ObjectUtils.isEmpty(crmParentData) ? Constant.TREE_PARENT_ID : crmParentData.getCrmDeptId();
                //如果不为空，说明数据已经存在。走更新部门的信息
                Result<Void> updateResult = crmRestManager.modifyDept(ei, crmData.getCrmDeptId(), originDept.getName(), parentCrmId, null);
                //更新中间表的信息
                crmData.setName(originDept.getName());
                crmData.setCrmParentId(parentCrmId);
                crmData.setUpdateTime(new Date());
                crmData.setDingParentId(originDept.getParentid());
                Integer count = updateDeptData(ei, crmData, appId);
                log.info("update originDept:{},crmData:{},count:{},result:{}", originDept, crmData, count, updateResult);
                //写入日志
//                Integer statusCode = updateResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
//                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), originDept.getName(), statusCode, updateResult.getErrorMessage());
//                logManager.writeLog(logWriteVo, "DepObj");
            } else {
                //不存在映射关系，就创建
                DeptVo vo = new DeptVo(ei, originDept.getName(), null, Constant.TREE_PARENT_ID);
                //如果是根级部门，直接创建，然后插入表格
                parentDeptVo = queryDeptData(ei, appId, originDept.getParentid());
                log.info("parentId:{},parentDeptVo:{},time:{}", originDept.getParentid(), parentDeptVo, System.currentTimeMillis());
                parentCrmId = ObjectUtils.isNotEmpty(parentDeptVo) ? parentDeptVo.getCrmDeptId() : Constant.TREE_PARENT_ID;
                vo.setCrmParentId(parentCrmId);
                Result<Integer> createResult = crmRestManager.createDept(vo);
                log.info("create result:{}", createResult);
                if (createResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                    //再次查询确认部门
                    DeptVo repeatName = queryDeptData(ei, appId, originDept.getId());
                    if (ObjectUtils.isNotEmpty(repeatName)) {
                        continue;
                    }
                    StringBuilder nameBuilder = new StringBuilder(vo.getName());
                    nameBuilder.append("__").append(String.valueOf(Math.round((Math.random() + 1) * 1000)));
                    vo.setName(nameBuilder.toString());
                    createResult = crmRestManager.createDept(vo);
                }
                CrmId = createResult.getData();
                //不处理crm已经存在同名或者在中间表没有映射已经存在的部门
                if (ObjectUtils.isNotEmpty(CrmId)) {
                    DeptVo deptVo = new DeptVo();
                    deptVo.setEi(ei);
                    deptVo.setDingDeptId(originDept.getId());
                    deptVo.setDingParentId(originDept.getParentid());
                    deptVo.setName(originDept.getName());
                    deptVo.setDingDeptOwner(originDept.getDeptOwner());
                    deptVo.setCrmDeptId(CrmId);
                    deptVo.setCreateTime(new Date());
                    deptVo.setUpdateTime(new Date());
                    deptVo.setCrmParentId(parentCrmId);
                    deptVo.setSeq(order);
                    Integer count = insertDeptData(Lists.newArrayList(deptVo), ei, Constant.SYSTEM_MANAGER, appId);
                    log.info("insert originDept:{},crmData:{},count:{}", originDept, crmData, count);
                }
                //写入日志
//                Integer statusCode = ObjectUtils.isNotEmpty(CrmId) ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
//                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), originDept.getName(), statusCode, createResult.getErrorMessage());
//                logManager.writeLog(logWriteVo, "DepObj");
            }
        }
        return Result.newSuccess();
    }

    //插入数据
    private Integer insertDeptData(List<DeptVo> deptVos, Integer ei, Integer userId, String appId) {
        Integer count = dingDeptService.initDeptManager(deptVos, ei, appId);
        return count;
    }

    //查询数据
    private DeptVo queryDeptData(Integer ei, String appId, Long deptId) {
        final String dingDeptId = String.valueOf(deptId);
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        DeptVo deptVo = dingDeptService.queryByDingId(ea, appId, dingDeptId);
        if (ObjectUtils.isNotEmpty(deptVo)) {
            return deptVo;
        }

        final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, dingDeptId);

        return StringUtils.isEmpty(fsDeptId) ? null : dingDeptService.queryByDingId(ea, appId, dingDeptId);
    }

    //修改数据
    private Integer updateDeptData(Integer ei, DeptVo deptVo, String appId) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        Result<Integer> count = dingDeptService.updateDept(deptVo, ea, appId);
        return count.getData();
    }


//    private Map<String, Object> fixSameDeptName(Map<Long, Dept> DingDeptMap, Dept dept, Circle circle, String ea) {
//        Dept parentDept = DingDeptMap.get(dept.getParentid());
//        MetaParam metaParam = new MetaParam(ea, Constant.SYSTEM_USER);
//        BeanResult<Circle> parentResult = circleService.getCircle(metaParam, circle.getParentId());
//        Map<String, Object> map = Maps.newHashMap();
//        if (ObjectUtils.isNotEmpty(parentResult.getResult()) && ObjectUtils.isNotEmpty(parentDept)) {
//            if (!parentDept.getName().equals(parentResult.getResult().getName()) && parentResult.getResult().getCircleId() != Constant.TREE_DEPT) {
//                StringBuilder builder = new StringBuilder();
//                String combineName = builder.append(parentDept.getName()).append("-").append(dept.getName()).toString();
//                map.put("same", Boolean.TRUE);
//                map.put("name", validName(combineName));
//                dept.setName(validName(combineName));
//                DingDeptMap.put(dept.getId(), dept);
//                log.info("fixsameDept combineName not same:{}", validName(combineName));
//                return map;
//            }
//        }
//        //如果上级部门同名
//        map.put("same", Boolean.FALSE);
//        map.put("name", validName(dept.getName()));
//        log.info("fixsameDept combineName:{}", validName(dept.getName()));
//        return map;
//    }

    //处理回调事件部门同名
    private Map<String, Object> fixCallBackName(Integer ei, Dept dept, String appId) {
        //回调事件处理变更
        Map<String, Object> map = Maps.newHashMap();
        //判断中间表的数据是否已经存在该部门
        List<DeptVo> deptName = dingDeptService.getDeptName(eieaConverter.enterpriseIdToAccount(ei), appId, dept.getName());
        Boolean isEquals = false;
        for (DeptVo vo : deptName) {
            if (vo.getDingDeptId().equals(dept.getId()) && vo.getCrmDeptId() != 0) {
                isEquals = true;
            }
        }

        if (!isEquals) {
            //如果不一致，拿enterprise的全局index++。默认一开始10000
            Result<DingEnterpriseResult> result = dingEnterpriseManager.queryEnterpriseByEi(ei);
            if (ObjectUtils.isNotEmpty(result.getData())) {
                Integer all_index = result.getData().getAllIndex() + 1;
                String finalName = dept.getName().concat(Symbol).concat(all_index.toString());
                //更新enterprise的信息
                DingEnterpriseResult enterpriseResult = result.getData();
                DingEnterprise enterprise = new DingEnterprise();
                BeanUtils.copyProperties(enterpriseResult, enterprise);
                enterprise.setAllIndex(all_index++);
                dingEnterpriseManager.updateEnterprise(enterprise);
                map.put("same", Boolean.TRUE);
                map.put("name", validName(finalName));
                return map;
            }
        }
        //如果前缀名字一致，确定是同名的，拿中间表的数据去当做部门名字
        map.put("same", Boolean.FALSE);
        map.put("name", validName(dept.getName()));
        return map;
    }

    //初始化员工
    public Result initDingEmployee(Integer ei, Integer userId, String appId) {
        log.info("init dingding emp start, ei={}.", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        //查询部门列表,调整获取应用所赋予的部门权限范围
        List<Dept> deptResponse = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), "1");
        if (CollectionUtils.isEmpty(deptResponse)) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, deptResponse);
            return Result.newError(ResultCode.DEPT_LIST_ERROR);
        }
        //查询部门下所有员工，并初始化到中间表
        Gson gson = new Gson();
        for (int i = 0; i < deptResponse.size(); i++) {
            Dept dept = gson.fromJson(gson.toJson(deptResponse.get(i)), Dept.class);
            List<User> deptUsers = DingRequestUtil.queryDeptUser(mappingEnterprise.getData().getClientIp(),
                    dept.getId(), appKey, appSecret, mappingEnterprise.getData().getToken());
            if (CollectionUtils.isEmpty(deptUsers)) {
                log.info("this dept no user, ei={},deptId={},deptName={}.", ei, dept.getId(), dept.getName());
                continue;
            }
            //保存钉钉员工信息
            Integer count = dingMappingEmployeeManager.initMappingEmployee(deptUsers, ei, dept.getId(), dept.getName(), appId);
            if (Objects.nonNull(count) && count.equals(deptUsers.size())) {
                log.info("初始化员工成功" + count + "条");
            } else {
                log.warn("初始化员工失败，deptId={},deptName={}.", dept.getId(), dept.getName());
            }
        }
        log.info("init dingding emp end, ei={}.", ei);
        return Result.newSuccess();
    }

    //全量拉取员工，比对数据库中的表
    public Result allPullEmployee(Integer ei, Integer userId, String appId) {
        StopWatch stopWatch = StopWatch.create("allPullEmployee");

        log.info("pullEmployee dingding emp startz1 ei={}.", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        final String outEa = mappingEnterprise.getData().getDingCorpId();
        //查询部门列表
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DEPT_LIST_ERROR);
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {
            List<Long> deptList = new ArrayList<>();
            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            if (scopeVo.getAuthedDept().get(0) == 1) {
                List<Dept> depts = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, mappingEnterprise.getData().getToken(), "1");

                List<Long> collect = depts.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(collect);
            } else {
                scopeVo.getAuthedDept().stream().forEach(item -> {
                    List<Dept> depts = DingRequestUtil.queryDeptList(mappingEnterprise.getData().getClientIp(),
                            appKey, appSecret, mappingEnterprise.getData().getToken(), item.toString());
                    List<Long> collect = depts.stream().map(Dept::getId)
                            .collect(Collectors.toList());
                    deptList.addAll(collect);

                });
                scopeVo.setAuthedDept(deptList);
            }
            //查询部门下所有员工，

            for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                //查询部门信息
                Dept dept = DingRequestUtil.queryDeptDetail(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i));

                List<User> deptUsers = DingRequestUtil.queryDeptUser(mappingEnterprise.getData().getClientIp(),
                        dept.getId(), appKey, appSecret, mappingEnterprise.getData().getToken());
                if (CollectionUtils.isEmpty(deptUsers)) {
                    log.info("this dept no user, ei={},deptId={},deptName={}.", ei, dept.getId(), dept.getName());
                    continue;
                }
                Gson gson = new Gson();
                //从数据库
                Result<List<DingMappingEmployeeResult>> employeeList = dingMappingEmployeeManager.queryEmpsBindByOutDeptId(ei, appId, outEa, dept.getId());

                HashMap<String, User> employeeDingMap = Maps.newHashMap();
                HashMap<String, User> employeeDataBaseMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(employeeList.getData())) {
                    employeeList.getData().parallelStream().forEach(item -> {
                        User user = new User(item.getDingUnionId(), item.getDingEmployeeId(), item.getDingEmployeeName(), item.getDingEmployeePhone(), StringUtils.EMPTY);
                        employeeDataBaseMap.put(user.getUserid(), user);

                    });
                }
                for (int j = 0; j < deptUsers.size(); j++) {
                    String itemValue = gson.toJson(deptUsers.get(j));
                    User user = gson.fromJson(itemValue, User.class);
                    employeeDingMap.put(user.getUserid(), user);

                }
                //已经在数据库中的钉钉员工与拉取过来的钉钉员工差异化比较，再存入数据库中。
                MapDifference<String, User> difference = Maps.difference(employeeDingMap, employeeDataBaseMap);
                Map<String, User> insertUserMap = difference.entriesOnlyOnLeft();
                //插入员工信息
                if (CollectionUtils.isNotEmpty(insertUserMap.entrySet()))
                    executorService.execute(new InsertEmployeeTask(dingMappingEmployeeManager, insertUserMap, ei, userId, dept.getId(), dept.getName(), appId));
//                    insertEmployee(insertUserMap, ei, userId, dept.getId(), dept.getName());
                //更新员工信息
                Sets.SetView<String> setDiffer = Sets.intersection(employeeDataBaseMap.keySet(), employeeDingMap.keySet());
                if (CollectionUtils.isNotEmpty(setDiffer))
                    executorService.execute(new UpdateEmployeeTask(setDiffer, employeeDataBaseMap, employeeDingMap, ei, appId));
                //删除员工信息
                Map<String, User> deleteMap = difference.entriesOnlyOnRight();
                log.info("delete ei:{}, employeeMap:{}", ei, deleteMap);
                if (MapUtils.isNotEmpty(deleteMap))
                    executorService.execute(new DeleteEmployeeTask(deleteMap, ei, appId));
            }
        }
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedUser())) {
            List<User> differEmployeeList = Lists.newArrayList();
            scopeVo.getAuthedUser().parallelStream().filter(item -> !isExistData(ei, item, appId)).forEach(item -> {
                //添加未加入的员工
                User user = DingRequestUtil.getUser(mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, item, mappingEnterprise.getData().getToken());
                differEmployeeList.add(user);
            });
            Integer count = dingMappingEmployeeManager.initMappingEmployee(differEmployeeList, ei, null, StringUtils.EMPTY, appId);
            if (Objects.nonNull(count) && count.equals(differEmployeeList.size())) {
                log.info("初始化员工成功" + count + "条");
            } else {
                log.warn("初始化员工失败,员工可能已经存在,deptId={},deptName={}.", differEmployeeList.get(0).getUserid(), differEmployeeList.get(0).getName());
            }
        }

        stopWatch.lap("allPullEmployee");
        stopWatch.log();
        return Result.newSuccess();
    }

    //根据用户钉钉的ID确定是否在数据库已经存在
    public boolean isExistData(Integer ei, String userId, String appId) {
        return dingMappingEmployeeManager.queryEmpByDingUserId(ei, userId, appId).getData() == null ? false : true;
    }

    //封装集合差集的方法，不改变原来集合的数据
    public static <T, K extends Collection<? extends T>> Set<T> sub(K a, K b) {
        Set<T> result = new HashSet<>();
        if (a != null) {
            result = new HashSet<>(a);
            result.removeAll(b);
        }
        return result;
    }

    //封装集合差集的方法，不改变原来集合的数据
    public static <T, K extends Collection<? extends T>> Set<T> retainAll(K a, K b) {
        Set<T> result = new HashSet<>();
        if (a != null) {
            result = new HashSet<>(a);
            result.retainAll(b);
        }
        return result;
    }

    @Override
    public Result<Map<String, Integer>> bindEmployee(List<DingMappingEmployeeResult> list, Integer ei, Integer employeeId, String appId) {
        //查询纷享所有员工
        Result<List<DingMappingEmployeeResult>> fxResult = getEmployeeFs(ei, appId);
        List<DingMappingEmployeeResult> employeeDtos = fxResult.getData();
        if (CollectionUtils.isNotEmpty(employeeDtos)) {
            //通过手机号比较哪些可以绑定
            for (DingMappingEmployeeResult employee : list) {
                employee.setEi(ei);
                employee.setUpdateBy(employeeId);
                employee.setCreateBy(employeeId);
                for (DingMappingEmployeeResult employeeDto : employeeDtos) {
                    if (StringUtils.isNotEmpty(employee.getDingEmployeePhone()) && StringUtils.isNotEmpty(employeeDto.getEmployeePhone())
                            && employee.getDingEmployeePhone().equals(employeeDto.getEmployeePhone())) {
                        employee.setEmployeePhone(employeeDto.getEmployeePhone());
                        employee.setEmployeeName(employeeDto.getEmployeeName());
                        employee.setEmployeeId(employeeDto.getEmployeeId());
                        employee.setEmployeeStatus(employeeDto.getEmployeeStatus());
                    } else if (StringUtils.isNotEmpty(employee.getDingEmployeeName()) && StringUtils.isNotEmpty(employeeDto.getEmployeeName())
                            && employee.getDingEmployeeName().equals(employeeDto.getEmployeeName()) && StringUtils.isEmpty(employeeDto.getDingEmployeePhone())) {
                        //适用于有些客户不肯授权手机号码做的兼容
                        //如果手机号匹配不上，根据名字匹配
                        employee.setEmployeePhone(employeeDto.getEmployeePhone());
                        employee.setEmployeeName(employeeDto.getEmployeeName());
                        employee.setEmployeeId(employeeDto.getEmployeeId());
                        employee.setEmployeeStatus(employeeDto.getEmployeeStatus());
                    }
                }
            }
        }
        //将数据保存到员工绑定表
        Integer count = dingMappingEmployeeManager.saveMappingEmployee(list, appId);
        Map<String, Integer> map = new HashMap<>();
        map.put("newEmpCount", count);
//        map.put("changeStatusCount", 0);
        return Result.newSuccess(map);
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> getEmployeeFs(Integer ei, String appId) {
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(ei);
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);

        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = null;
        try {
            GetAllEmployeeIdsArg getAllEmployeeIdsArg = new GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setRunStatus(RunStatus.ALL);
            getAllEmployeeIdsArg.setEnterpriseId(ei);
            GetAllEmployeeIdsResult ids = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);

            batchGetEmployeeDtoArg.setEmployeeIds(ids.getEmployeeIds());
            batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        } catch (OrganizationException e) {
            log.error("获取纷享职员信息失败，exception = {}.", e);
            return Result.newError(ResultCode.GET_FXEMP_FAILED);
        }

        List<EmployeeDto> employeeDtoList = batchGetEmployeeDtoResult.getEmployeeDtos();
        List<DingMappingEmployeeResult> list = new ArrayList<>();
        for (EmployeeDto employeeDto : employeeDtoList) {
            DingMappingEmployeeResult dingMappingEmployeeResult = new DingMappingEmployeeResult();
            dingMappingEmployeeResult.setEmployeeId(employeeDto.getEmployeeId());
            dingMappingEmployeeResult.setEmployeeName(employeeDto.getName());
            dingMappingEmployeeResult.setEmployeePhone(employeeDto.getMobile());
            dingMappingEmployeeResult.setEmployeeStatus(employeeDto.getStatus().getValue());
            list.add(dingMappingEmployeeResult);
        }

        return Result.newSuccess(list);
    }

    @Override
    public Result<Integer> deleteBind(Integer ei, String dingEmployeeId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId)) {
            log.warn("deleteBind param ei or dingEmployeeId is null.");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //解绑员工，不能真正删除中间表数据，而是将状态改为0
        return Result.newSuccess(dingMappingEmployeeManager.relieveBind(ei, dingEmployeeId, appId));
    }

    @Override
    public Result<Integer> createFxEmployee(EmployeeVo vo, String appId) {
        //获取企业mobileStatus
        GetConfigDto.Argument argument = new GetConfigDto.Argument();
        String key = EnterpriseConfigKey.CONFIG_KEY_NEW_EMP_MOBILE_SETTING.getKey();
        argument.setKey(key);
        argument.setEmployeeId(CURRENT_EMPLOYEE_ID);
        argument.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
        argument.setEnterpriseId(vo.getEi());
        GetConfigDto.Result configResult = enterpriseConfigService.getConfig(argument);
        String configValue = configResult.getValue();

        CreateEmployeeArg arg = new CreateEmployeeArg();
        arg.setName(vo.getName());
        arg.setFullName(vo.getName());
        arg.setGender(StringUtils.isNotEmpty(vo.getGender()) ? vo.getGender() : "F");
        arg.setMobile(vo.getMobile());
        arg.setTelephone(vo.getMobile());
        arg.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
        arg.setDepartmentIds(new ArrayList<>());
        arg.setEnterpriseId(vo.getEi());
        arg.setMobileStatus(MobileStatus.valueOf(Integer.valueOf(configValue)));
        arg.setAccount(vo.getMobile());
        CreateEmployeeResult result = null;
        Integer count = 0;
        DingSyncApi syncApi = new DingSyncApi();
        syncApi.setApiName(EMP_OBJ);
        syncApi.setSyncDirection(2);
        DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(vo.getEi(), syncApi, vo.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
        String detail = "dingding：create apiName[{" + EMP_OBJ + "}], [" + vo.getName() + "]";
        String request = null;
        String response = null;
        try {
            result = employeeAdapterService.createEmployee(arg);
            if (Objects.isNull(result) || Objects.isNull(result.getEmployee())) {
                String content = String.format("[%s] 创建纷享员工异常，数据流向为[%s]", vo.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
                DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_FAIL.getStatus(), 1, 0, 1, SyncLogStatusEnum.NO_RECOVER.getStatus());
                dingSyncLogManager.logDingSync(kcSyncLogDO);
                log.warn("创建纷享职员失败,arg=[{}],CreateEmployee.Result = [{}].", argument, result);
                return Result.newError(ResultCode.MODIFY_FXEMP_FAILED);
            } else {
                request = new Gson().toJson(argument);
                response = new Gson().toJson(result);
                String content = String.format("[%s] 创建纷享员工成功，数据流向为[%s]", vo.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
                DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, request, response, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 1, 0, SyncLogStatusEnum.SUCCESS.getStatus());
                log.info("[" + vo.getEi() + "]:createFxEmployee apiName[{}], result=[{}].", EMP_OBJ, result);
                dingSyncLogManager.logDingSync(kcSyncLogDO);
            }
        } catch (OrganizationException e) {
            String content = String.format("[%s] 创建纷享员工异常，数据流向为[%s]", vo.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
            DynamicParamUtil.supplementLogDO(kcSyncLogDO, e.getMessage(), null, null, content, OperationStatusEnum.SYNC_FAIL.getStatus(), 1, 0, 1, SyncLogStatusEnum.NO_RECOVER.getStatus());
            dingSyncLogManager.logDingSync(kcSyncLogDO);
            log.warn("创建纷享员工异常,arg=[{}],e=[{}].", arg, e.toString());
            Result createResult = Result.newError(ResultCode.CREATE_FXEMP_FAILED);
            createResult.setErrorDescription(e.getMessage());
            return createResult;
        }
        //创建之后建立绑定关系
        DingMappingEmployeeResult dingMappingEmployeeResult = new DingMappingEmployeeResult();
        dingMappingEmployeeResult.setEmployeeName(vo.getName());
        dingMappingEmployeeResult.setDingEmployeeName(vo.getName());
        dingMappingEmployeeResult.setEmployeePhone(vo.getMobile());
        dingMappingEmployeeResult.setDingEmployeePhone(vo.getMobile());
        dingMappingEmployeeResult.setEmployeeId(result.getEmployee().getEmployeeId());
        dingMappingEmployeeResult.setDingEmployeeId(vo.getDingEmployeeId());
        dingMappingEmployeeResult.setEmployeeStatus(1);
        dingMappingEmployeeResult.setDingEmployeeStatus(1);
        dingMappingEmployeeResult.setEi(vo.getEi());
        dingMappingEmployeeResult.setCreateBy(vo.getUpdateBy());
        dingMappingEmployeeResult.setUpdateBy(vo.getUpdateBy());
        List<DingMappingEmployeeResult> list = new ArrayList<>();
        list.add(dingMappingEmployeeResult);
        dingMappingEmployeeManager.saveMappingEmployee(list, appId);
        count++;
        return Result.newSuccess(count);
    }

    //传递部门信息创建员工
    @Override
    public Result<Integer> batchCreateFxEmployee(List<CreateCrmEmployeeVo> vo, Integer ei, Integer userId, String appId) {
        //获取该ei下面的部门映射
        StopWatch stopWatch = StopWatch.create("batchCreateFxEmployee--" + ei);
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        Map<Long, DeptVo> deptMap = Maps.newHashMap();
        List<DeptVo> deptByEI = dingDeptService.getDeptByEI(fsEa, appId);
        Map<Long, DeptVo> deptMaps = Maps.newHashMap();
        deptByEI.stream().forEach(item -> deptMaps.put(item.getDingDeptId(), item));
        deptMap.putAll(deptMaps);
        stopWatch.lap("getDeptInfos");
        for (int i = 0; i < vo.size(); i++) {
            CreateCrmEmployeeVo item = vo.get(i);
            DeptVo dingDeptEntity = dingDeptService.queryByDingId(fsEa, appId, String.valueOf(item.getDingDeptId()));

            DingTalkEmployeeObject dingEmp = outerOaEmployeeDataManager.findDingTalkEmployeeObject(ChannelEnum.dingding, fsEa, item.getDingEmployeeId(), appId);
            List<Integer> depts = new ArrayList<>();
            if (dingDeptEntity == null || Objects.isNull(dingEmp)) {
                dingDeptEntity = dingDeptService.queryByDingId(fsEa, appId, String.valueOf(item.getDingDeptId()));
                if (ObjectUtils.isEmpty(dingDeptEntity)) {
                    //存在部门已经删除的状态
                    //删除已经存在的
                    dingMappingEmployeeManager.physicalDeleteByDeptId(ei, item.getDingEmployeeId(), item.getDingDeptId(), appId);
                    UserVo userDetail = DingRequestUtil.getUserDetail(tokenManager.getToken(ei, appId), item.getDingEmployeeId(), enterprise.getData().getClientIp());
                    if (ObjectUtils.isEmpty(userDetail)) return Result.newError(ResultCode.PARAMS_ERROR);
                    //更新员工信息
                    if (CollectionUtils.isNotEmpty(userDetail.getDepartment())) {
                        for (int j = 0; j < userDetail.getDepartment().size(); j++) {
                            Long temp = userDetail.getDepartment().get(j);
                            DeptVo deptVo = dingDeptService.queryByDingId(fsEa, appId, String.valueOf(temp));
                            if (ObjectUtils.isEmpty(deptVo)) continue;

                            //钉钉创建员工时，会同时发送创建以及编辑。插入失败就不需要创建员工。让成功的创建员工
                            //这里其实是锁,如果状态已经为0,修改失败,就让另一个线程处理后面的事情
                            final OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindManager.queryByFsEaAndOutEmpId(ChannelEnum.dingding, fsEa, appId, userDetail.getUserid());
                            log.info("check outerOaEmployeeBindEntity exists, outerOaEmployeeBindEntity:{}", outerOaEmployeeBindEntity);
                            if (Objects.nonNull(outerOaEmployeeBindEntity)) {
                                return Result.newSuccess();
                            }
                        }
                    }
                    dingDeptEntity = dingDeptService.queryByDingId(fsEa, appId, String.valueOf(userDetail.getDepartment().get(0)));
                }
            }
            //重新查询一次
            dingEmp = outerOaEmployeeDataManager.findDingTalkEmployeeObject(ChannelEnum.dingding, fsEa, item.getDingEmployeeId(), appId);
            List<Integer> deptIds = treeDeptIds(deptMap, dingEmp.getDeptId(), new ArrayList<>());
            depts.addAll(deptIds);

            depts.remove(0);
            //主属部门
            item.setCrmMainDeptId(deptMap.get(item.getDingDeptId()).getCrmDeptId().toString());
            //附属部门 底层json数据需要转换成String
            List<String> viceDepts = depts.stream().map(String::valueOf).collect(Collectors.toList());
            item.setCrmViceDepts(viceDepts);
            //设置汇报对象
            item.setLeader(dingDeptEntity.getCrmDeptOwner());
            String legalName = item.getName();//employeeValidName(item.getName());
            item.setName(legalName);
            item.setEi(ei);

            DeptVo deptVo = dingDeptService.queryByDingId(fsEa, appId, String.valueOf(item.getDingDeptId()));
            final String deptVoName = Objects.isNull(deptVo) ? null : deptVo.getName();

            // 初始化员工信息,创建fs员工的时候用到
            // 创建员工数据
            DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
            employeeData.setUnionId(item.getUnionid());
            employeeData.setSexType(item.getGender());
            employeeData.setUserid(item.getDingEmployeeId());
            employeeData.setName(legalName);
            employeeData.setPhone(item.getMobile());
            employeeData.setDeptId(item.getDingDeptId());
            employeeData.setDeptName(deptVoName);
            outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, entity.getId());

            final com.facishare.open.outer.oa.connector.common.api.result.Result<Integer> addResult = createFsEmployee(ei, appId, entity, item);

            //加入日志
            Integer statusCode = addResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
            LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), item.getName(), statusCode, addResult.getMsg());
            logManager.writeLog(logWriteVo, "EmpObj");
            if (ObjectUtils.isEmpty(addResult.getData()))
                return Result.newError(addResult.getCode(), addResult.getMsg());

            stopWatch.lap("createEmployee");
        }
        stopWatch.lap("dbSaveEmployee");
        stopWatch.log();
        return Result.newSuccess();
    }

    public com.facishare.open.outer.oa.connector.common.api.result.Result<Integer> createFsEmployee(Integer ei, String appId, OuterOaEnterpriseBindEntity entity, CreateCrmEmployeeVo item) {
        final String fsEmpId = outerOaEmployeeBindManager.getFsEmpIdByEaAndOutEmpId(ChannelEnum.dingding, appId, entity.getFsEa(), entity.getOutEa(), item.getDingEmployeeId());
        if (StringUtils.isNotEmpty(fsEmpId)) {
            // 写入绑定表
            outerOaEmployeeBindManager.upsert(new OuterOaEmployeeBindEntity(IdGenerator.get(), ChannelEnum.dingding, entity.getId(), entity.getFsEa(), entity.getOutEa(), appId, fsEmpId, item.getDingEmployeeId(), BindStatusEnum.normal, System.currentTimeMillis(), System.currentTimeMillis()));
            return new com.facishare.open.outer.oa.connector.common.api.result.Result<>(Integer.valueOf(fsEmpId));
        }

        com.facishare.open.outer.oa.connector.common.api.result.Result<Integer> addResult;
        com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> createResult = objectDataManager.createEmployee(entity, item.getDingEmployeeId());
        Integer empId = Optional.ofNullable(createResult.getData())
                .map(ActionAddResult::getObjectData)
                .map(objectData -> objectData.getInt("user_id"))
                .orElse(null);
        if (createResult.getCode() == ResultCode.EMPLOYEE_MOBILE_EXIST.getErrorCode()) {
            //通过手机号搜索员工
            Object result = crmRestManager.queryByField(ei, "phone", item.getMobile()).getData().get("user_id");
            if (ObjectUtils.isNotEmpty(result)) empId = Integer.parseInt(result.toString());
            //修改员工
            item.setCrmEmpId(empId);
            outerOaEmployeeBindManager.upsert(new OuterOaEmployeeBindEntity(IdGenerator.get(), ChannelEnum.dingding, entity.getId(), entity.getFsEa(), entity.getOutEa(), appId, String.valueOf(empId), item.getDingEmployeeId(), BindStatusEnum.normal, System.currentTimeMillis(), System.currentTimeMillis()));
            final com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> updateResultResult = objectDataManager.updateEmpData(entity, item.getDingEmployeeId());
            createResult = new com.facishare.open.outer.oa.connector.common.api.result.Result<>(updateResultResult.getCode(), updateResultResult.getMsg(), null);
        }
        if (createResult.getCode() == ResultCode.EMPLOYEE_NAME_IS_EXIST.getErrorCode()) {
            //同名处理
            String phone = item.getMobile();
            String suffix = StringUtils.isNotEmpty(phone) ? phone.substring(phone.length() - 4, phone.length()) : String.valueOf(Math.round((Math.random() + 1) * 1000));
//            item.setName(employeeValidName(item.getName().concat(suffix)));
            item.setName(item.getName().concat(suffix));
            updateEmployeeName(appId, entity, item.getDingEmployeeId(), item.getName());
            createResult = objectDataManager.createEmployee(entity, item.getDingEmployeeId());
            if (createResult.isSuccess()) empId = createResult.getData().getObjectData().getInt("user_id");
        }
        addResult = new com.facishare.open.outer.oa.connector.common.api.result.Result<>(createResult.getCode(), createResult.getMsg(), empId);
        return addResult;
    }


    //递归获取附属部门
    public List<Integer> treeDeptIds(Map<Long, DeptVo> deptMaps, Long dingDeptId, List<Integer> ids) {
//        List<Long> ids = new ArrayList<>();
        if ((null != dingDeptId) && (deptMaps.get(dingDeptId) != null)) {
            final DeptVo deptVo = deptMaps.get(dingDeptId);
            Integer crmDeptId = deptVo.getCrmDeptId();
            Long dingDeptID = deptVo.getDingParentId();
            ids.add(crmDeptId.intValue());
            treeDeptIds(deptMaps, dingDeptID, ids);
        }
        return ids;
    }

    @Override
    public Result<Void> stopFxEmployee(Integer ei, Integer fxEmpId, String fxEmpName, String appId, String dingUserId) {
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        final com.facishare.open.outer.oa.connector.common.api.result.Result<Void> stopResult = objectDataManager.removeEmpData(entity, dingUserId, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
        Integer statusCode = stopResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.DELETE.getType(), fxEmpName, statusCode, stopResult.getMsg());
        logManager.writeLog(logWriteVo, "EmpObj");
        return Result.newError(stopResult.getCode(), stopResult.getMsg());
    }

    /**
     * 钉钉员工变动后，更新绑定关系表以及绑定的纷享员工
     *
     * @param corpId
     * @param userIds
     * @return
     */
    @Override
    public Result<Void> updateEmp(String corpId, List<String> userIds, String appId) {
        StopWatch stopWatch = StopWatch.create("updateEmp");
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByDingCorpId(corpId, appId);
        String accessToken = DingRequestUtil.getToken(enterprise.getData().getClientIp(), enterprise.getData().getAppKey(), enterprise.getData().getAppSecret());

        if (!enterprise.isSuccess() || ObjectUtils.isEmpty(accessToken)) {
            log.warn("updateDingEmp:the enterprise not bind, corpId=[{}].", corpId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        //查询集成模式是1：同步员工 2：组织架构
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) {
            syncEmployeeFir(corpId, userIds, enterprise, appId);
        } else {
            syncEmployeeSec(corpId, userIds, accessToken, enterprise.getData().getClientIp(), enterprise.getData(), appId);
        }
        stopWatch.lap("updateEmp");
        stopWatch.log();
        return Result.newSuccess();
    }

    /**
     * 同步模式1
     */
    public Result<Void> syncEmployeeFir(String corpId, List<String> userIds, Result<DingEnterpriseResult> enterprise, String appId) {
        for (String userId : userIds) {
            //查询用户详情
            User user = DingRequestUtil.getUser(enterprise.getData().getClientIp(), enterprise.getData().getAppKey(),
                    enterprise.getData().getAppSecret(), userId, enterprise.getData().getToken());
            if (Objects.isNull(user)) {
                log.warn("get dingding user result is null, corpId={}, userId={}.", corpId, userId);
                return Result.newError(ResultCode.GET_DING_EMP_FAILED);
            }
            String name = user.getName();
            String phone = user.getMobile();
//            int status = userGetResponse.getActive() ? 1 : 2;
            //修改绑定关系表
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setEi(enterprise.getData().getEi());
            dingMappingEmployee.setDingEmployeeId(userId);
            dingMappingEmployee.setDingUnionId(user.getUnionid());
            dingMappingEmployee.setDingEmployeeName(name);
            dingMappingEmployee.setDingEmployeePhone(phone);
            dingMappingEmployee.setDingEmployeeStatus(1);

            dingMappingEmployee.setCreateBy(OPERATOR);
            dingMappingEmployee.setUpdateBy(OPERATOR);
            DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee, appId);
            int bindStatus = 0;
            Integer fxEmpId = 0;
            if (Objects.isNull(mappingEmp)) {
                dingMappingEmployeeManager.insertDingEmp(dingMappingEmployee, appId);
            } else {
                bindStatus = mappingEmp.getBindStatus();
                fxEmpId = mappingEmp.getEmployeeId();
                dingMappingEmployeeManager.updateDingEmpByDingId(dingMappingEmployee, appId);
            }
            //修改纷享员工
            if (bindStatus == EmpStatusEnum.BIND.getStatus()) {
                final OuterOaEnterpriseBindEntity enterpriseBind = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, enterprise.getData().getEa(), appId);
                objectDataManager.updateEmpData(enterpriseBind, userId);
            }
        }
        return Result.newSuccess();
    }

    /**
     * 同步模式2
     */
    public Result<Void> syncEmployeeSec(String corpId, List<String> userIds, String accessToken, String clientIp, DingEnterpriseResult enterpriseResult, String appId) {
        StopWatch stopWatch = StopWatch.create("syncEmployeeSec" + corpId);
        Integer ei = enterpriseResult.getEi();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        for (String userId : userIds) {
            // 上个分布式锁 redis
            String uuid = UUID.randomUUID().toString();
            if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), userId, uuid, 15)) {
                //查询用户详情
                UserVo user = DingRequestUtil.getUserDetail(accessToken, userId, clientIp);
                if (ObjectUtils.isEmpty(user) || CollectionUtils.isEmpty(user.getDepartment())) {
                    log.info("syncEmployee not user:{},corpId:{},userIds:{}", corpId, corpId, userIds);
                    return Result.newSuccess();
                }
                if (Objects.isNull(user)) {
                    log.warn("get dingding user result is null, corpId={}, userId={}.", corpId, userId);
                    return Result.newError(ResultCode.GET_DING_EMP_FAILED);
                }
                stopWatch.lap("queryUser");
                User item = new User();
                BeanUtils.copyProperties(user, item);
                List<User> users = Lists.newArrayList();
                users.add(item);
                DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findByDingEmpId(ei, user.getUserid(), appId);
                int bindStatus = 0;
                Integer fxEmpId = 0;
                if (Objects.isNull(mappingEmp)) {
                    //根据钉钉部门ID查询crm
                    //如果客户存在多个主属部门，应该插入多条记录
                    if (CollectionUtils.isNotEmpty(user.getDepartment())) {

                        for (int i = 0; i < user.getDepartment().size(); i++) {
                            Long temp = user.getDepartment().get(i);
                            DeptVo deptVo = dingDeptService.queryByDingId(ea, appId, String.valueOf(temp));
                            if (ObjectUtils.isEmpty(deptVo)) continue;
                            Integer count = dingMappingEmployeeManager.insertModeEmployee(users, ei, deptVo.getDingDeptId(), Constant.SYSTEM_MANAGER, deptVo.getName(), deptVo.getCrmDeptId(), appId);
                            log.info("insert dingMapping:{}", count);
                            //钉钉创建员工时，会同时发送创建以及编辑。插入失败就不需要创建员工。让成功的创建员工
                            if (count == 0) return Result.newSuccess();
                        }
                    }
                    stopWatch.lap("insert model");
                    //在CRM创建员工
                    CreateCrmEmployeeVo vo = new CreateCrmEmployeeVo();
                    BeanUtils.copyProperties(user, vo);
                    vo.setDingEmployeeId(user.getUserid());
                    vo.setDingDeptId(user.getDepartment().get(0));
                    List<CreateCrmEmployeeVo> voList = Lists.newArrayList();
                    voList.add(vo);
                    log.info("createFxEmployee:volist:{},ei:{}", voList, ei);
                    batchCreateFxEmployee(voList, ei, Constant.SYSTEM_MANAGER, appId);
                    stopWatch.lap("batchCreateFxEmployee");
                } else {
                    bindStatus = mappingEmp.getBindStatus();
                    if (bindStatus == 0) {
                        //避免出现未绑定的员工出现获取employeeId未空的情况
                        CreateCrmEmployeeVo vo = new CreateCrmEmployeeVo();
                        BeanUtils.copyProperties(user, vo);
                        vo.setDingEmployeeId(user.getUserid());
                        vo.setDingDeptId(user.getDepartment().get(0));
                        List<CreateCrmEmployeeVo> voList = Lists.newArrayList();
                        voList.add(vo);
                        batchCreateFxEmployee(voList, ei, Constant.SYSTEM_MANAGER, appId);
                        return Result.newSuccess();
                    }
                    fxEmpId = mappingEmp.getEmployeeId();

                    //根据所属的user返回的主属部门，修改信息
                    DeptVo deptVo = dingDeptService.queryByDingId(ea, appId, String.valueOf(user.getDepartment().get(0)));
                    Integer crmDept = deptVo.getCrmDeptId();
                    List<DeptVo> deptByEI = dingDeptService.getDeptByEI(ea, appId);
                    HashMap<Long, DeptVo> deptMap = Maps.newHashMap();
                    deptByEI.stream().forEach(itemCrmDept -> deptMap.put(itemCrmDept.getDingDeptId(), itemCrmDept));
                    //附属部门的信息
                    List<Integer> viceDepts = new ArrayList<>();
                    user.getDepartment().stream().forEach(itemDept -> {
                        List<Integer> deptIds = treeDeptIds(deptMap, itemDept, new ArrayList<>());
                        viceDepts.addAll(deptIds);
                    });
                    log.info("modify fxemp vicedept:{},ei:{},userid:{}", viceDepts, ei, userId);

                    // 必须先更新EmployeeData, 在更新Crm,objectDataManager用到了
                    updateDingMapping(ei, userId, user, deptMap, fxEmpId, appId);

                    String name = validName(user.getName());
                    final OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
                    com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> updateResult = objectDataManager.updateEmpData(enterpriseBindEntity, userId);
                    if (updateResult.getCode() == ResultCode.EMPLOYEE_NAME_IS_EXIST.getErrorCode()) {
                        String phone = user.getMobile();
                        String suffix = StringUtils.isNotEmpty(phone) ? phone.substring(phone.length() - 5, phone.length() - 1) : String.valueOf(Math.round((Math.random() + 1) * 1000));
                        name = name + suffix;
                        updateEmployeeName(appId, enterpriseBindEntity, user.getUserid(), name);
                        updateResult = objectDataManager.updateEmpData(enterpriseBindEntity, userId);
                    }

                    Integer statusCode = updateResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                    LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), name, statusCode, updateResult.getMsg());
                    logManager.writeLog(logWriteVo, "EmpObj");
                }

            }
            RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), userId, uuid);
        }
        return Result.newSuccess();

    }

    private void updateEmployeeName(String appId, OuterOaEnterpriseBindEntity enterpriseBindEntity, String userId, String name) {
        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataManager.queryByChannelAndAppIdAndEmpId(ChannelEnum.dingding, appId, enterpriseBindEntity.getOutEa(), userId);
        final DingTalkEmployeeObject employeeObject = dataEntity.getOutUserInfo().toJavaObject(DingTalkEmployeeObject.class);
        employeeObject.setName(name);
        outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeObject), ChannelEnum.dingding, enterpriseBindEntity.getId());
    }

    private void updateDingMapping(Integer ei, String dingUserId, UserVo user, HashMap<Long, DeptVo> deptMap, Integer fxEmpId, String appId) {
        //根据dingEmpID查询数据，如果不在user.getDept中的数据删除，没有的插入，有的更新
        List<DingMappingEmployeeResult> mappingData = dingMappingEmployeeManager.findDingEmpIdList(ei, dingUserId, appId);
        Map<Long, DingMappingEmployeeResult> maps = mappingData.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingDeptId, Function.identity(), (key1, key2) -> key2));

        user.getDepartment().stream().forEach(itemUpdate -> {
            DeptVo deptVo = deptMap.get(itemUpdate);
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setEi(ei);
            dingMappingEmployee.setDingEmployeeId(dingUserId);
            dingMappingEmployee.setDingUnionId(user.getUnionid());
            dingMappingEmployee.setDingEmployeeName(user.getName());
            dingMappingEmployee.setEmployeeName(validName(user.getName()));
            dingMappingEmployee.setEmployeeId(fxEmpId);
            dingMappingEmployee.setEmployeePhone(user.getMobile());
            dingMappingEmployee.setDingEmployeePhone(user.getMobile());
            dingMappingEmployee.setDingEmployeeStatus(1);
            dingMappingEmployee.setDingDeptId(itemUpdate);
            dingMappingEmployee.setCreateBy(OPERATOR);
            dingMappingEmployee.setUpdateBy(OPERATOR);
            dingMappingEmployee.setCrmDeptId(deptVo.getCrmDeptId());
            dingMappingEmployee.setBindStatus(EmpStatusEnum.BIND.getStatus());
            dingMappingEmployee.setDingDeptName(deptVo.getName());
            if (ObjectUtils.isNotEmpty(maps.get(itemUpdate))) {
                //更新
                dingMappingEmployeeManager.updateModelEmp(dingMappingEmployee, appId);
                log.info("objectMapping update dingMapping service ei:{},arg:{}", ei, dingMappingEmployee);
                maps.remove(itemUpdate);
            } else {
                //插入
                dingMappingEmployeeManager.insertAllModelData(Lists.newArrayList(dingMappingEmployee), ei, appId);
                log.info("objectMapping insert dingMapping service ei:{},arg:{}", ei, dingMappingEmployee);
                maps.remove(itemUpdate);
            }
        });
        if (CollectionUtils.isNotEmpty(maps.keySet())) {
            //删除中间表的数据
            maps.keySet().stream().forEach(itemDelete -> {
                log.info("objectMapping delete dingMapping service ei:{},userid:{},arg:{}", ei, dingUserId, itemDelete);
                dingMappingEmployeeManager.deleteByDeptId(ei, dingUserId, itemDelete, appId);
            });
        }
    }


    /**
     * 钉钉删除员工，同步停用纷享员工，解绑（删除）绑定关系
     *
     * @param corpId
     * @param dingUserIds
     * @return
     */
    @Override
    public Result<Void> stopEmp(String corpId, List<String> dingUserIds, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByDingCorpId(corpId, appId);
        if (!enterprise.isSuccess()) {
            log.warn("stopDingEmp:the enterprise not bind, corpId=[{}].", corpId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        Integer ei = enterprise.getData().getEi();
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        for (String dingUserId : dingUserIds) {
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setEi(enterprise.getData().getEi());
            dingMappingEmployee.setDingEmployeeId(dingUserId);
            //停用纷享员工
            DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee, appId);
            if (Objects.nonNull(mappingEmp) && Objects.nonNull(mappingEmp.getEmployeeId())) {
                String fxEmpName = mappingEmp.getEmployeeName();
                final com.facishare.open.outer.oa.connector.common.api.result.Result<Void> stopResult = objectDataManager.removeEmpData(entity, dingUserId, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
                Integer statusCode = stopResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.DELETE.getType(), fxEmpName, statusCode, stopResult.getMsg());
                logManager.writeLog(logWriteVo, "EmpObj");
            }

        }
        return Result.newSuccess();
    }

    @Override
    public Result<DingMappingEmployeeResult> queryEmployeeByUnionId(Integer ei, String unionId, String appId) {
        return dingMappingEmployeeManager.queryEmpByUnionId(ei, unionId, appId);
    }

    @Override
    public Result<DingMappingEmployeeResult> queryEmpByDingUserId(Integer ei, String dingUserId, String appId) {
        return dingMappingEmployeeManager.queryEmpByDingUserId(ei, dingUserId, appId);
    }

    /**
     * 批量查询已绑定的纷享员工，需要同步待办消息
     *
     * @param offset
     * @param limit
     * @return
     */
    @Override
    public Result<List<BindFxUserResult>> getBindEiAndUser(Integer offset, Integer limit, String appId) {
        if (Objects.isNull(offset) || Objects.isNull(limit)) {
            log.warn("getBindEiAndUser param is null");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (limit > 1000) {
            log.warn("query count is out of limit, maximum is 1000");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return dingMappingEmployeeManager.queryBindFxEmp(offset, limit, appId);
    }

    @Override
    public Result<List<BindFxEaResult>> getBindEa(Integer offset, Integer limit, String appId) {
        if (Objects.isNull(offset) || Objects.isNull(limit)) {
            log.warn("getBindEa param is null");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (limit > 1000) {
            log.warn("query count is out of limit, maximum is 1000");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return dingEnterpriseManager.queryBindFxEa(offset, limit);
    }

    @Override
    public Result<ConditionEmployeeResult> conditionEmployee(QueryEmployeeVo vo, Integer ei, String appId) {
        Integer count = dingMappingEmployeeManager.conditionEmployeeCount(ei, appId, vo.getBindStatus(), vo.getDingNameOrPhone(), vo.getDingDeptId());
        List<DingMappingEmployeeResult> data = dingMappingEmployeeManager.conditionQueryEmployee(ei, appId, vo.getBindStatus(), vo.getPageNumber(), vo.getPageSize(), vo.getDingNameOrPhone(), vo.getDingDeptId()).getData();
        ConditionEmployeeResult conditionEmployeeResult = new ConditionEmployeeResult();
        conditionEmployeeResult.setTotalCount(count);
        conditionEmployeeResult.setDataList(data);
        return Result.newSuccess(conditionEmployeeResult);
    }

    @Override
    public Result<List<DeptVo>> conditionDepts(Integer enterpriseId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        return Result.newSuccess(dingDeptService.getDeptByEI(fsEa, appId));
    }

    //创建部门（插入表，创建crm部门)
    @Override
    public Result<Integer> createFxDept(String corpId, List<Long> depts, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByDingCorpId(corpId, appId);
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) return Result.newSuccess();

        Integer ei = enterprise.getData().getEi();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        Integer allCount = 0;
        if (!enterprise.isSuccess()) {
            log.warn("updateDingEmp:the enterprise not bind, corpId=[{}].", corpId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        for (Long deptID : depts) {
            //查询部门详情
            Dept dept = DingRequestUtil.queryDeptDetail(enterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), enterprise.getData().getToken(), deptID);
            if (Objects.isNull(dept)) {
                log.warn("get dingding dept result is null, corpId={}, userId={}.", corpId, deptID);
                return Result.newError(ResultCode.QUERY_DEPT_DETAIL);
            }
            //创建crm部门
            //查询父级部门
            DeptVo deptVo = dingDeptService.queryByDingId(ea, appId, String.valueOf(dept.getParentid()));
            log.info("query deptVo deptVo:{}", deptVo);
            // 如果上级部门不存在，则查询该部门的所有上级部门并创建
            if (ObjectUtils.isEmpty(deptVo) && dept.getParentid() != 1) {
                //查询没有创建的上级部门列表并创建
                allCount += createNotFoundDept(ei, enterprise, deptID, ea, appId);
            } else {
                allCount += createCrmDept(dept, ObjectUtils.isEmpty(deptVo) ? Constant.TREE_PARENT_ID : deptVo.getCrmDeptId(), ei, ea, appId);
            }

        }
        return Result.newSuccess(allCount);
    }

    //查询所有的上级部门，没有创建的创建
    private Integer createNotFoundDept(Integer ei, Result<DingEnterpriseResult> enterprise, Long deptID, String ea, String appId) {

        List<Long> listParent = DingRequestUtil.getListParent(enterprise.getData().getClientIp(), deptID, tokenManager.getToken(ei, appId));
        //翻转列表
        Collections.reverse(listParent);
        Integer count = 0;
        for (int i = 0; i < listParent.size(); i++) {
            //查询列表详情，找出父级parentId
            Long deptId = listParent.get(i);
            DeptVo deptVo = dingDeptService.queryByDingId(ea, appId, String.valueOf(deptId));
            if (ObjectUtils.isEmpty(deptVo)) {
                Dept deptDetail = DingRequestUtil.getDeptDetail(tokenManager.getToken(ei, appId), enterprise.getData().getClientIp(), deptId);
                DeptVo parentVo = dingDeptService.queryByDingId(ea, appId, String.valueOf(deptDetail.getParentid()));
                Integer parentId = ObjectUtils.isEmpty(deptDetail.getParentid()) ? Constant.TREE_PARENT_ID : parentVo.getCrmDeptId();
                Integer temp = createCrmDept(deptDetail, parentId, ei, ea, appId);
                count += temp;
            }
        }
        return count;
    }


    private Integer createCrmDept(Dept dept, Integer parentId, Integer ei, String ea, String appId) {
        //先插入dept，避免批量更新
        DeptVo vo = new DeptVo();
        vo.setEi(ei);
        final Long deptId = dept.getId();
        vo.setDingDeptId(deptId);
        vo.setDingParentId(dept.getParentid());
        vo.setName(validName(dept.getName()));
        vo.setDingDeptOwner(dept.getDeptOwner());
        vo.setCrmDeptId(0);//避免空指针唯一索引约束失效
        vo.setCrmParentId(parentId);
        if (dingDeptService.addDeptByEi(vo, ea, appId) == 0) {
            return 0;
        }
        final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, String.valueOf(deptId));
        if (fsDeptId != null) {
            return 0;
        }

        //查询负责人
        Result<DingMappingEmployeeResult> queryResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, dept.getDeptOwner(), appId);
        Integer crmEmployeeId = queryResult.getData() == null ? null : queryResult.getData().getEmployeeId();
        vo.setCrmDeptOwner(crmEmployeeId);
        Result<Integer> createDeptResult = crmRestManager.createDept(vo);
        Map<String, Object> sameMap = Maps.newHashMap();

        if (createDeptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
            sameMap = fixCallBackName(ei, dept, appId);
            if (Boolean.valueOf(sameMap.get("same").toString())) {
                //创建部门
                vo.setName(sameMap.get("name").toString());
                createDeptResult = crmRestManager.createDept(vo);
                dept.setName(sameMap.get("name").toString());
            }
        }
        if (createDeptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode() || !createDeptResult.isSuccess()) {
            //如果处理完同名的部门后，还是同名就返回 删除中间表的数据
            Integer statusCode = createDeptResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
            String message = createDeptResult.getErrorMessage() + "\t" + vo.toString();
            LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), vo.getName(), statusCode, message);
            logManager.writeLog(logWriteVo, "DepObj");

            return 0;
        }
        vo.setCrmDeptId(createDeptResult.getData());
        vo.setCrmParentId(parentId);
        vo.setUpdateTime(new Date());
        Result<Integer> updateDept = dingDeptService.updateDept(vo, ea, appId);
        if (updateDept.getData() != 0) {
            log.info("insert ding_dept row:{},ei:{},deptVo:{}", updateDept.getData(), ei, vo);
        }
        Integer statusCode = createDeptResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        String message = createDeptResult.getErrorMessage() + "\t" + vo.toString();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), vo.getName(), statusCode, message);
        logManager.writeLog(logWriteVo, "DepObj");
        //重新设置dept
//        List<DeptVo> deptByEI = dingDeptMananger.getDeptByEI(ei);
//        Map<Long, DeptVo> deptMaps = Maps.newHashMap();
//        deptByEI.stream().forEach(item -> deptMaps.put(item.getDingDeptId(), item));
//        redisDingService.saveDeptInfos(String.valueOf(ei), deptMaps);
        return updateDept.getData();
    }


    //更新部门（修改部门表以及crm部门)
    @Override
    public Result<Integer> modifyFxDept(String corpId, List<Long> depts, String appId) {

        StopWatch stopWatch = StopWatch.create("modifyFxDept");
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByDingCorpId(corpId, appId);
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) return Result.newSuccess();
        Integer ei = enterprise.getData().getEi();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        Integer allCount = 0;
        if (!enterprise.isSuccess()) {
            log.warn("updateDingEmp:the enterprise not bind, corpId=[{}].", corpId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {

                for (Long deptID : depts) {
                    //查询部门详情
                    Dept dingDept = DingRequestUtil.queryDeptDetail(enterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), enterprise.getData().getToken(), deptID);
                    if (Objects.isNull(dingDept)) {
                        log.warn("get dingding dept result is null, corpId={}, userId={}.", corpId, deptID);
                    }
                    //查询部门的时候，看有没有创建。如果没有则触发创建部门createFxDept
                    DeptVo dbDeptVo = dingDeptService.queryByDingId(ea, appId, String.valueOf(deptID));
                    if (ObjectUtils.isEmpty(dbDeptVo)) {
                        createFxDept(corpId, Lists.newArrayList(deptID), appId);
                    }
                    //这边做个过滤如果判断名字是否一致，以前缀的"__"名字是否一致
                    if (!dingDept.getParentid().equals(dbDeptVo.getDingParentId()) || suffixName(dingDept.getName(), dbDeptVo.getName()) || compareDeptOwner(dingDept.getDeptOwner(), dbDeptVo.getDingDeptOwner())) {
                        //如果上级部门变动，需要更新crm的上级部门
                        DeptVo deptParent = dingDeptService.queryByDingId(ea, appId, String.valueOf(dingDept.getParentid()));
                        Integer parentId = Constant.TREE_PARENT_ID;
                        if (ObjectUtils.isNotEmpty(deptParent)) {
                            parentId = Optional.ofNullable(deptParent.getCrmDeptId()).orElse(Constant.TREE_PARENT_ID);
                        }
                        //查询部门管理员
                        Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, dingDept.getDeptOwner(), appId);
                        Integer crmEmployeeId = dingMappingEmployeeResultResult.getData() == null ? null : dingMappingEmployeeResultResult.getData().getEmployeeId();

                        Result<Void> modifyResult = crmRestManager.modifyDept(ei, dbDeptVo.getCrmDeptId(), validName(dingDept.getName()), parentId, crmEmployeeId);
                        if (modifyResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                            //如果部门名称已经存在
                            Map<String, Object> sameMap = fixCallBackName(ei, dingDept, appId);
                            if (Boolean.valueOf(sameMap.get("same").toString())) {
                                //如果是true，名字前缀不一致，通过index后缀修改
                                //更新部门上下级关系
                                String newName = validName(sameMap.get("name").toString());
                                modifyResult = crmRestManager.modifyDept(ei, dbDeptVo.getCrmDeptId(), newName, parentId, crmEmployeeId);
                                dingDept.setName(sameMap.get("name").toString());
                            }
                        }
                        //写入日志
                        Integer statusCode = modifyResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), dbDeptVo.getName(), statusCode, modifyResult.getErrorMessage());
                        logManager.writeLog(logWriteVo, "DepObj");
                        //更新数据库
                        //更新名字/上级部门/主管
                        dbDeptVo.setName(dingDept.getName());
                        dbDeptVo.setDingParentId(dingDept.getParentid());
                        dbDeptVo.setDingDeptOwner(dingDept.getDeptOwner());
                        dbDeptVo.setCrmDeptOwner(crmEmployeeId);
                        dbDeptVo.setCrmParentId(parentId);
                        dingDeptService.updateDept(dbDeptVo, ea, appId);
                    }
                }
                stopWatch.lap("modifyFxDept");
                stopWatch.log();
            }
        });
        return Result.newSuccess(allCount);
    }

    private Boolean suffixName(String deptName, String dataName) {
        String[] names = dataName.split("__");
        if (deptName.equals(names[0])) {
            return false;
        }
        return true;
    }

    // StringUtils.isNoneBlank(dept.getDeptOwner(),deptVo.getDingDeptOwner()) ||!dept.getDeptOwner().equals(deptVo.getDingDeptOwner()

    private Boolean compareDeptOwner(String deptOwner, String dingOwner) {
        if (StringUtils.isAllBlank(deptOwner, dingOwner)) {
            return false;
        }
        if (deptOwner != null && !deptOwner.equals(dingOwner)) {
            return true;
        }
        if (dingOwner != null && !dingOwner.equals(deptOwner)) {
            return true;
        }
        return false;
    }


    //删除部门（删除部门数据以及crm部门)
    @Override
    public Result<Integer> removeFxDept(String corpId, List<Long> depts, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByDingCorpId(corpId, appId);
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) return Result.newSuccess();

        Integer ei = enterprise.getData().getEi();
        Integer allCount = 0;
        if (!enterprise.isSuccess()) {
            log.warn("updateDingEmp:the enterprise not bind, corpId=[{}].", corpId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        List<DeptVo> deptVos = dingDeptService.getDeptByEI(ea, appId);
        for (Long deptID : depts) {
            List<DeptVo> dataBaseDept = deptVos.stream().filter(item -> item.getDingDeptId().equals(deptID)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dataBaseDept)) {
                DeptVo baseDept = dataBaseDept.get(0);
                Result<Void> stopResult = crmRestManager.stopDept(baseDept);
                //写入日志
                Integer statusCode = stopResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.DELETE.getType(), baseDept.getName(), statusCode, stopResult.getErrorMessage());
                logManager.writeLog(logWriteVo, "DepObj");
                //删除数据库
                Result<Integer> integerResult = dingDeptService.deleteDept(ea, String.valueOf(deptID), appId);
                allCount++;
            }
        }
        if (allCount != 0) {
            log.info("delete crm dept ,ei:{},deptIDs:{}", ei, depts);
        }
        return Result.newSuccess(allCount);
    }

    @Override
    public Result<Integer> removeDataDept(Integer corpId, Integer id, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(corpId);
        Integer count = dingDeptService.fixDept(fsEa, id, appId);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Void> createDeptOwner(String clientIp, String accessToken, String token, Integer ei, String appId) {
        //请求遍历
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        List<DeptVo> baseDept = dingDeptService.getDeptByEI(ea, appId);
        //创建部门负责人，默认把负责人在crm创建员工
        if (CollectionUtils.isNotEmpty(baseDept)) {
            log.info("baseDept size:{}", baseDept.size());
            //创建负责人
            for (int i = 0; i < baseDept.size(); i++) {
                DeptVo deptVo = baseDept.get(i);
                Dept dept = DingRequestUtil.queryDeptDetail(clientIp, accessToken, token, deptVo.getDingDeptId());
                if (StringUtils.isNoneEmpty(dept.getDeptOwner())) {
                    deptVo.setDingDeptOwner(dept.getDeptOwner());
                    Integer crmEmpId = null;
                    Result<DingMappingEmployeeResult> crmEmpResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, dept.getDeptOwner(), appId);
                    if (ObjectUtils.isNotEmpty(crmEmpResult.getData()) && ObjectUtils.isNotEmpty(crmEmpResult.getData().getEmployeeId())) {
                        //说明员工已经创建
                        crmEmpId = crmEmpResult.getData().getEmployeeId();
                    } else {
                        //创建员工
                        DingMappingEmployeeResult data = crmEmpResult.getData();
                        CreateCrmEmployeeVo createCrmEmployeeVo = new CreateCrmEmployeeVo();
                        createCrmEmployeeVo.setMobile(data.getDingEmployeePhone());
                        createCrmEmployeeVo.setName(data.getDingEmployeeName());
                        createCrmEmployeeVo.setEi(data.getEi());
                        createCrmEmployeeVo.setDingDeptId(data.getDingDeptId());
                        createCrmEmployeeVo.setGender(data.getGender());
                        createCrmEmployeeVo.setId(data.getId().intValue());
                        createCrmEmployeeVo.setUpdateBy(data.getUpdateBy());
                        createCrmEmployeeVo.setDingEmployeeId(data.getDingEmployeeId());
                        List<CreateCrmEmployeeVo> createCrmEmployeeVosList = Lists.newArrayList();
                        createCrmEmployeeVosList.add(createCrmEmployeeVo);
                        batchCreateFxEmployee(createCrmEmployeeVosList, ei, createCrmEmployeeVo.getUpdateBy(), appId);
                        crmEmpResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, dept.getDeptOwner(), appId);
                        crmEmpId = crmEmpResult.getData().getEmployeeId();
                    }
                    deptVo.setCrmDeptOwner(crmEmpId);
                    //更新部门信息
                    Result<Void> modifyResult = crmRestManager.modifyDept(ei, deptVo.getCrmDeptId(), deptVo.getName(), deptVo.getCrmParentId(), deptVo.getCrmDeptOwner());
                    log.info("trace crmowner modify dept:{}.result:{}", deptVo, modifyResult);
                    if (modifyResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                        StringBuilder nameBuilder = new StringBuilder(deptVo.getName());
                        nameBuilder.append("__").append(String.valueOf(Math.round((Math.random() + 1) * 1000)));
                        deptVo.setName(nameBuilder.toString());
                        modifyResult = crmRestManager.modifyDept(ei, deptVo.getCrmDeptId(), deptVo.getName(), deptVo.getCrmParentId(), deptVo.getCrmDeptOwner());
                        dingDeptService.updateDept(deptVo, ea, appId);
                    }
                    //加入日志
                    Integer statusCode = modifyResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                    LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), deptVo.getName(), statusCode, modifyResult.getErrorMessage());
                    logManager.writeLog(logWriteVo, "DepObj");
                }

            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> independenceAgainMapping(Integer ei, String appId) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        //先处理dingDept为0的部门，在crm创建
        DeptVo dataDept = dingDeptService.queryByDingId(ea, appId, "*********");
        dataDept.setCrmDeptId(8574);
        dingDeptService.updateDept(dataDept, ea, appId);
        StopWatch stopWatch = StopWatch.create("againEmployeeMapping");
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
        String clientIp = enterpriseResult.getData().getClientIp();
        //获取crm的人员信息
//      //获取全部dingDeptMap
        List<DeptVo> deptList = dingDeptService.getDeptByEI(ea, appId);
        Map<Long, DeptVo> deptMaps = deptList.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));
        QueryEmployeeVo vo = new QueryEmployeeVo();
        vo.setBindStatus(2);
        Result<ConditionEmployeeResult> result = this.conditionEmployee(vo, ei, appId);
        log.info("condition employee size:{}", result.getData().getDataList().size());
        //获取到list数据
        List<DingMappingEmployeeResult> emps = result.getData().getDataList();
        Map<String, DingMappingEmployeeResult> employeeDtoMap = emps.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingEmployeeId, Function.identity(), (key1, key2) -> key2));
        for (int i = 0; i < emps.size(); i++) {
            DingMappingEmployeeResult employeeResult = emps.get(i);
            executorService.submit(new againMappingTask(employeeAdapterService, deptMaps, ei, employeeResult.getDingEmployeeId(), employeeResult.getEmployeeId(), employeeResult.getDingEmployeePhone(), employeeResult.getDingDeptId(), clientIp, tokenManager.getToken(ei, appId)));
        }
        stopWatch.lap("againEmployeeMapping");
        stopWatch.log();
        log.info("again mapping list end");
        return null;
    }

    //统一处理特殊字符或者空格的情况
    private String validName(String name) {
        String match = "[^\\-\\\\/\\[\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name);
        String result = m.replaceAll("-");
        return result;
    }
//    [^\\-·\\[\\]【】()（）_a-zA-Z0-9\u4E00-\u9fAF\u3400-\u4dBF\u3300-\u33FF\uF900-\uFAFF]

    private String employeeValidName(String name) {
        String match = "[^\\-·\\[\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name.trim());
        String result = m.replaceAll("-");
        return result;
    }

}
