package com.facishare.open.ding.web.limiter;

import com.facishare.open.ding.web.constants.ConfigCenter;
import com.google.common.util.concurrent.RateLimiter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CrmRateLimiter {
    private static final ConcurrentHashMap rateLimiterMap = new ConcurrentHashMap<String, RateLimiter>();

    private static final String default_rate_limit = "defaultRateLimit";

    private static final Long max_limit_time_out = 10L;
    /**
     * 初始化
     * @param key
     * @return
     */
    private static RateLimiter getRateLimiter(String key) {
        key = StringUtils.isNotEmpty(key) ? key : default_rate_limit;
        if (rateLimiterMap.get(key) == null) {
            Map<String, Integer> objectMap = new Gson().fromJson(ConfigCenter.CRM_RATE_LIMIT, new TypeToken<Map<String, Integer>>() {
            });
            if(objectMap.containsKey(key)) {
                rateLimiterMap.put(key, RateLimiter.create(objectMap.get(key)));
            }
        }
        return (RateLimiter) rateLimiterMap.get(StringUtils.isNotEmpty(key) ? key : default_rate_limit);
    }

    public static boolean isAllowed(String key) {
        long start = System.currentTimeMillis();
        boolean isAllowedByKey = getRateLimiter(key).tryAcquire(max_limit_time_out, TimeUnit.SECONDS);
        log.info("CrmRateLimiter.isAllowed,key={},isAllowedByKey={},time= {} ms",
                key, isAllowedByKey, (System.currentTimeMillis() - start));
        return isAllowedByKey;
    }
}
