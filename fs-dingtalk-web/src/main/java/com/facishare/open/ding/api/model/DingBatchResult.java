package com.facishare.open.ding.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/24 19:47
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingBatchResult implements Serializable {
    private Integer errcode;
    private Result result;
    @Data
    public static class Result implements Serializable {
        private Boolean has_more;
        private String next_cursor;
        private List<DingBatchData> values;
        private String request_id;

    }

}
