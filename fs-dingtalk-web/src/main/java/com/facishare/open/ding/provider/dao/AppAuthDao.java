package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.UserAppResult;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.AppAuthEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/10 16:12
 * @Version 1.0
 */
public interface AppAuthDao extends ICrudMapper<AppAuthEntity> {


    @Select("<script>" +
            "select * from app_author_info where ding_corp_id=#{dingCorpId}" +
            "<if test='appId!=null'>" +
            "and app_id=#{appId}" +
            "</if>" +
            "<if test='suiteId!=null'>" +
            "and suite_id=#{suiteId}" +
            "</if>" +
            "</script>")
    List<AppAuthResult> conditionAppAuth(@Param("dingCorpId") String dingCorpId, @Param("appId") Long appId, @Param("suiteId") Long suiteId);


    @Insert("<script>" +
            "insert ignore into app_author_info" +
            "(ei,ding_corp_id,agent_id,app_id,suite_id,auth_info) values " +
            "(#{appAuth.ei},#{appAuth.dingCorpId},#{appAuth.agentId},#{appAuth.appId},#{appAuth.suiteId},#{appAuth.authInfo})" +
            "</script>")
    Integer insertAppAuth(@Param("appAuth") AppAuthEntity appAuthEntity);

    @Select("<script>" +
            "select * from app_author_info where ei=#{ei}" +
            "<if test='id!=null'>" +
            "and id>#{id}" +
            "</if>" +
            "order by id " +
            "limit #{limit}" +
            "</script>")
    List<AppAuthEntity> pageById(@Param("ei") Integer enterpriseId, @Param("id") Long id, @Param("limit") int limit);

    @Select("select distinct ei from app_author_info")
    List<String> getAllTenantIds();


    /**
     * 迁移order的时候,需要将suiteId->appId
     * 根据suiteId获取第一条数据
     */
    @Select("select * from app_author_info where ei=#{ei} and suite_id=#{suiteId} limit 1")
    AppAuthEntity getFirstBySuiteId(@Param("ei") Integer ei, @Param("suiteId") Long suiteId);
}
