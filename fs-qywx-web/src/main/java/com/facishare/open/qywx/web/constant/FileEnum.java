package com.facishare.open.qywx.web.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 * @Date 2021/3/19 16:26
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum FileEnum {
    IMAGE_TYPE("image", "I"),
    VOICE_TYPE("voice", "A"),
    TEXT_TYPE("text", "T"),
    VIDEO_TYPE("video", "V"),
    FILE_TYPE("file", "D"),
    EXTERNAL_REDPACKET("external_redpacket", "T"),
    LOCATION("location", "T"),
    WEAPP("weapp", "T"),
    LINK("link", "T"),

    ;

    private String messageType;
    private String fsMessageType;//对应纷享的文件类型

    public static FileEnum getType(String type) {
        for (FileEnum fileEnum : FileEnum.values()) {
            if (fileEnum.messageType.equals(type)) {
                return fileEnum;
            }
        }
        return null;
    }


}
