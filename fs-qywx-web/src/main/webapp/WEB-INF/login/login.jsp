<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.net.URLEncoder" %>

<!DOCTYPE html>
<html>
<head>
    <!-- 引入jweixin JS文件 -->
    <script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
    <script src="//a9.fspage.com/open/cdn/jquery/2.2.4/jquery.min.js"></script>
    <script type="text/javascript">
        $(function(){
            // 显示处理中
            $('#loadingContainer').show();
            function handleError(errorMsg, propose) {
                // 隐藏处理中
                $('#loadingContainer').hide();
                // 显示错误容器
                $('#errorContainer').show();
                // 设置错误信息
                $('#errorMessage').text(errorMsg);
                $('#errorPropose').text(propose);
            }
            function handleSuccess(msg) {
                // 隐藏处理中
                $('#loadingContainer').hide();
                // 显示成功容器
                $('#successContainer').show();
                // 设信息
                $('#successMessage').text(msg);
            }

            // 调用接口请求需要的参数回来
            $.ajax({
                url: "/open/qyweixin/createJsapiSignature",
                data: {
                    // 当前网页的URL，不包含#及其后面部分，签名算法的时候会用到
                    url: window.location.href.split("#")[0],
                    // 获取企业微信的corpid，用于后续签名算法
                    outEa: "<c:out value='${requestScope.corpId}'/>",
                    appId: "<c:out value='${requestScope.appId}'/>"
                },
                type: "get",
                success: function (res) {
                    // 如果res是字符串，需要先解析成对象
                    let responseData = typeof res === 'string' ? JSON.parse(res) : res;
                    if(responseData.errorCode!=="s120050000"){
                        handleError('<c:out value="${requestScope.createJsapiSignatureErrorMsg}"/>', responseData.errorMsg);
                    }
                    console.log('create js api signature success');
                    wx.config({
                        beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: responseData.data.appId, // 必填，企业微信的corpid，必须与当前登录的企业一致
                        timestamp: responseData.data.timestamp, // 必填，生成签名的时间戳
                        nonceStr: responseData.data.nonceStr, // 必填，生成签名的随机串
                        signature: responseData.data.signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
                        jsApiList: ['openDefaultBrowser'] // 必填，传入需要使用的接口名称
                    });
                    console.log('create js api signature set data');
                    handleSuccess('<c:out value="${requestScope.loginSuccess}"/>');
                    wx.ready(function(){
                        openDefaultBrowser();
                    });

                    wx.error(function(res){
                        // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
                        console.log(res);
                        handleError('<c:out value="${requestScope.createJsapiSignatureErrorMsg}"/>', '<c:out value="${requestScope.loginErrorPropose}"/>');
                    });
                }
            });

            function generateUrl() {
                try {
                    var loginRedirectUrl = '<c:out value="${requestScope.loginRedirectUrl}"/>';
                    console.log('Signature API call loginRedirectUrl:', loginRedirectUrl);
                    // 解码URL
                    var decodedUrl = decodeURIComponent(loginRedirectUrl);
                    console.log('Signature API call decodedUrl:', decodedUrl);
                    return decodedUrl;
                } catch (e) {
                    console.log('Failed to generate URL:', e);
                    handleError('<c:out value="${requestScope.loginErrorMsg}"/>', '<c:out value="${requestScope.loginErrorPropose}"/>');
                    return '';
                }
            }

            function openDefaultBrowser() {
                var url = generateUrl();
                wx.invoke('openDefaultBrowser', {
                    'url': url
                }, function(res){
                    console.log('res------------->', res);
                    // 如果res是字符串，需要先解析成对象
                    let responseData = typeof res === 'string' ? JSON.parse(res) : res;
                    if (responseData.err_msg != "openDefaultBrowser:ok") {
                        // 错误处理
                        handleError('<c:out value="${requestScope.loginErrorMsg}"/>', '<c:out value="${requestScope.loginErrorPropose}"/>');
                    }else{
                        handleSuccess('<c:out value="${requestScope.loginSuccess}"/>');
                    }
                });
            }
        });
    </script>
    <title><c:out value="${requestScope.errorPageTitle}"/></title>
</head>
<body>
<!-- 添加处理中提示 -->
<div id="loadingContainer" style="display: none; color: blue; margin: 20px; padding: 10px; border: 1px solid blue;">
    <h3>loading</h3>
</div>
<!-- 添加错误信息展示区域 -->
<div id="errorContainer" style="display: none; color: red; margin: 20px; padding: 10px; border: 1px solid red;">
    <h3>ERROR</h3>
    <p id="errorMessage"></p>
    <p id="errorPropose"></p>
</div>
<div id="successContainer" style="display: none; color: black; margin: 20px; padding: 10px; border: 1px solid black;">
    <h3>Success</h3>
    <p id="successMessage"></p>
</div>
</body>
</html>